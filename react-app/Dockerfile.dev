# Development Dockerfile for React Frontend
FROM node:18-alpine

# Set working directory
WORKDIR /app

# Install system dependencies for better performance
RUN apk add --no-cache git

# Install dependencies
COPY package*.json ./
RUN npm install

# Copy configuration files
COPY tailwind.config.js ./
COPY postcss.config.js ./

# Copy source code
COPY . .

# Expose port
EXPOSE 3001

# Start development server
CMD ["npm", "start"]
