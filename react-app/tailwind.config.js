/** @type {import('tailwindcss').Config} */
module.exports = {
  content: [
    "./src/**/*.{js,jsx,ts,tsx}",
    "./public/index.html",
  ],
  theme: {
    extend: {
      colors: {
        'fatbeam-blue': '#15a7dd',
        'fatbeam-dark': '#1397c7',
      },
      fontFamily: {
        'serif': ['Georgia', 'serif'],
      },
      animation: {
        'float': 'float 6s ease-in-out infinite',
        'gentle-zoom': 'gentle-zoom 4s ease-in-out infinite',
        'pulse': 'pulse 2s ease-in-out infinite',
      },
      keyframes: {
        float: {
          '0%, 100%': {
            transform: 'translateY(0) scale(1)',
            opacity: '0.2',
          },
          '50%': {
            transform: 'translateY(-20px) scale(1.5)',
            opacity: '0.5',
          },
        },
        'gentle-zoom': {
          '0%': {
            transform: 'scale(1)',
          },
          '50%': {
            transform: 'scale(1.2)',
          },
          '100%': {
            transform: 'scale(1)',
          },
        },
        pulse: {
          '0%': {
            opacity: '0.4',
            transform: 'translate(-50%, -50%) scale(1)',
          },
          '50%': {
            opacity: '0.6',
            transform: 'translate(-50%, -50%) scale(1.2)',
          },
          '100%': {
            opacity: '0.4',
            transform: 'translate(-50%, -50%) scale(1)',
          },
        },
      },
    },
  },
  plugins: [],
}
