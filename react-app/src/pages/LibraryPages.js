import React, { useState, useEffect } from 'react';

const LibraryPages = () => {
  const [bookmarkedItems, setBookmarkedItems] = useState([]);
  const [checkedOutItems, setCheckedOutItems] = useState([]);
  const [selectedCategory, setSelectedCategory] = useState('all');
  const [selectedDifficulty, setSelectedDifficulty] = useState('all');
  const [searchQuery, setSearchQuery] = useState('');
  const [showBookDetails, setShowBookDetails] = useState(false);
  const [selectedBook, setSelectedBook] = useState(null);
  const [filteredBooks, setFilteredBooks] = useState([]);
  const [hoveredBook, setHoveredBook] = useState(null);

  useEffect(() => {
    const handleClickOutside = () => {
      setHoveredBook(null);
    };

    document.addEventListener('click', handleClickOutside);

    return () => {
      document.removeEventListener('click', handleClickOutside);
    };
  }, []);

  // Add styles for book animation
  useEffect(() => {
    const style = document.createElement('style');
    style.textContent = `
    .transform-origin-left {
      transform-origin: left;
    }
    `;
    document.head.appendChild(style);
    return () => {
      document.head.removeChild(style);
    };
  }, []);

  const allBooks = [
    {
      id: 'b1',
      title: 'Network Protocols Overview',
      author: 'Professor Elara Waveweaver',
      description: 'A comprehensive guide to network protocols and their implementations in enterprise networks.',
      difficulty: 'intermediate',
      category: 'protocols',
      wikiLink: '/networking/protocols/overview',
      isUnique: false,
      spineColor: 'bg-gradient-to-r from-[#3E2723] via-[#4E342E] to-[#3E2723]',
      textColor: 'text-[#C0C0C0]',
      coverImage: 'https://readdy.ai/api/search-image?query=Ancient%20magical%20book%20cover%20with%20network%20patterns%20and%20ethereal%20glowing%20symbols%20floating%20on%20a%20rich%20leather%20background%2C%20professional%20photography%20with%20dramatic%20lighting&width=400&height=600&seq=book1&orientation=portrait'
    },
    {
      id: 'b5',
      title: 'Fatbeam Rural Network Solutions',
      author: 'Dr. Mystic Bytecaster',
      description: 'Unique case study on implementing high-speed networks in rural communities with Fatbeam solutions.',
      difficulty: 'advanced',
      category: 'protocols',
      wikiLink: '/fatbeam/case-studies/rural-networks',
      isUnique: true,
      spineColor: 'bg-gradient-to-r from-[#8B7355] via-[#A0856E] to-[#8B7355]',
      textColor: 'text-[#D4AF37]',
      coverImage: 'https://readdy.ai/api/search-image?query=Mystical%20book%20cover%20featuring%20flowing%20data%20streams%20and%20magical%20energy%20patterns%20on%20aged%20leather%20with%20gold%20accents%2C%20professional%20studio%20lighting&width=400&height=600&seq=book5&orientation=portrait'
    },
    {
      id: 'b6',
      title: 'Quantum Enchantment',
      author: 'Sage Quantumweaver',
      description: 'Advanced concepts in quantum networking and magical entanglement.',
      difficulty: 'advanced',
      category: 'fundamentals',
      spineColor: 'bg-gradient-to-r from-[#5D4037] via-[#6D4C41] to-[#5D4037]',
      textColor: 'text-[#C0C0C0]',
      coverImage: 'https://readdy.ai/api/search-image?query=Quantum%20physics%20inspired%20magical%20book%20cover%20with%20intricate%20geometric%20patterns%20on%20deep%20purple%20leather%2C%20professional%20photography&width=400&height=600&seq=book6&orientation=portrait'
    },
    {
      id: 'b7',
      title: 'Runic Network Defense',
      author: 'Guardian Shieldmaster',
      description: 'Essential protection spells for magical network security.',
      difficulty: 'intermediate',
      category: 'security',
      spineColor: 'bg-gradient-to-r from-[#4E342E] via-[#5D4037] to-[#4E342E]',
      textColor: 'text-[#C0C0C0]',
      coverImage: 'https://readdy.ai/api/search-image?query=Ancient%20protective%20runes%20and%20magical%20shields%20on%20dark%20green%20leather%20book%20cover%20with%20metallic%20embellishments%2C%20professional%20studio%20lighting&width=400&height=600&seq=book7&orientation=portrait'
    },
    {
      id: 'b8',
      title: 'Celestial Connections',
      author: 'Astral Networker',
      description: 'Establishing and maintaining connections through celestial planes.',
      difficulty: 'advanced',
      category: 'protocols',
      spineColor: 'bg-gradient-to-r from-[#3E2723] via-[#4E342E] to-[#3E2723]',
      textColor: 'text-[#C0C0C0]',
      coverImage: 'https://readdy.ai/api/search-image?query=Celestial%20map%20and%20constellation%20patterns%20on%20midnight%20blue%20leather%20book%20cover%20with%20silver%20details%2C%20professional%20photography%20with%20dramatic%20lighting&width=400&height=600&seq=book8&orientation=portrait'
    },
    {
      id: 'b2',
      title: 'Troubleshooting the Ethereal Web',
      author: 'Dr. Thorne Cablemancer',
      description: 'Learn the art of identifying and resolving network disruptions across magical connections.',
      difficulty: 'intermediate',
      category: 'troubleshooting',
      spineColor: 'bg-[#2A1B0E]',
      textColor: 'text-[#C0C0C0]',
      coverImage: 'https://readdy.ai/api/search-image?query=Mystical%20book%20cover%20featuring%20intricate%20network%20troubleshooting%20diagrams%20with%20magical%20aura%2C%20ancient%20leather%20texture%20background%20with%20professional%20studio%20lighting&width=400&height=600&seq=book2&orientation=portrait'
    },
    {
      id: 'b3',
      title: 'Fundamentals of Magical Networking',
      author: 'Sage Bytewarden',
      description: 'Essential concepts and principles of magical network architecture for beginners.',
      difficulty: 'beginner',
      category: 'fundamentals',
      spineColor: 'bg-[#2A1B0E]',
      textColor: 'text-[#C0C0C0]',
      coverImage: 'https://readdy.ai/api/search-image?query=Enchanted%20book%20cover%20with%20basic%20networking%20symbols%20and%20magical%20runes%2C%20elegant%20leather%20binding%20with%20soft%20mystical%20glow%2C%20professional%20product%20photography&width=400&height=600&seq=book3&orientation=portrait'
    },
    {
      id: 'b4',
      title: 'Advanced Spellbound Security',
      author: 'Master Cryptkeeper',
      description: 'Advanced techniques for securing magical networks against dark forces.',
      difficulty: 'advanced',
      category: 'security',
      spineColor: 'bg-[#2A1B0E]',
      textColor: 'text-[#C0C0C0]',
      coverImage: 'https://readdy.ai/api/search-image?query=Dark%20mysterious%20book%20cover%20with%20security%20sigils%20and%20protective%20magical%20barriers%2C%20ancient%20leather%20texture%20with%20metallic%20accents%2C%20professional%20studio%20lighting&width=400&height=600&seq=book4&orientation=portrait'
    }
  ];

  useEffect(() => {
    let results = allBooks;
    if (searchQuery) {
      const query = searchQuery.toLowerCase();
      results = results.filter(book =>
        book.title.toLowerCase().includes(query) ||
        book.author.toLowerCase().includes(query) ||
        book.description.toLowerCase().includes(query)
      );
    }
    if (selectedCategory !== 'all') {
      results = results.filter(book => book.category === selectedCategory);
    }
    if (selectedDifficulty !== 'all') {
      results = results.filter(book => book.difficulty === selectedDifficulty);
    }
    setFilteredBooks(results);
  }, [searchQuery, selectedCategory, selectedDifficulty]);

  const BookSpine = ({ book }) => {
    return (
      <div
        className={`h-[300px] w-[60px] ${book.spineColor} cursor-pointer transition-all duration-500 hover:scale-105 hover:z-10 relative shadow-xl overflow-hidden group`}
        onMouseEnter={() => setHoveredBook(book)}
        onMouseLeave={() => setHoveredBook(null)}
        onClick={() => {
          setSelectedBook(book);
          setShowBookDetails(true);
        }}
      >
        <div className="absolute inset-0 flex items-center justify-center">
          <div className="absolute inset-0">
            {/* Enhanced spine edges and embossing */}
            <div className="absolute top-0 left-0 right-0 h-[8px] bg-gradient-to-b from-black/50 to-transparent"></div>
            <div className="absolute bottom-0 left-0 right-0 h-[8px] bg-gradient-to-t from-black/50 to-transparent"></div>
            <div className="absolute inset-y-0 left-0 w-[4px] bg-gradient-to-r from-black/50 to-transparent"></div>
            <div className="absolute inset-y-0 right-0 w-[4px] bg-gradient-to-l from-black/50 to-transparent"></div>

            {/* Book title with enhanced styling */}
            <div className="absolute inset-0 flex items-center justify-center">
              <span className={`${book.textColor} text-sm font-serif vertical-text whitespace-nowrap transform -rotate-90 px-4 z-10 group-hover:scale-105 transition-transform duration-300 drop-shadow-lg tracking-wider`}>
                {book.title}
              </span>
            </div>
          </div>
        </div>
      </div>
    );
  };

  return (
    <div className="min-h-screen bg-[#0a0500] pt-20">
      <header className="bg-[#1a0f00] border-b border-[#3a2a15] p-6">
        <div className="container mx-auto flex justify-between items-center">
          <h1 className="text-3xl text-amber-100 font-bold">Fatbeam University Library</h1>
          <div className="flex items-center gap-4">
            <div className="relative">
              <input
                type="text"
                placeholder="Search books..."
                className="bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg pl-10 focus:outline-none focus:ring-2 focus:ring-amber-500"
                value={searchQuery}
                onChange={(e) => setSearchQuery(e.target.value)}
              />
              <i className="fas fa-search absolute left-3 top-1/2 transform -translate-y-1/2 text-amber-500"></i>
            </div>
            <button className="bg-amber-700 text-amber-100 px-4 py-2 rounded-lg hover:bg-amber-600 transition-colors rounded-button">
              <i className="fas fa-bookmark mr-2"></i>
              Bookmarked ({bookmarkedItems.length})
            </button>
          </div>
        </div>
      </header>

      <main className="container mx-auto p-8">
        <div className="flex gap-6 mb-8">
          <div className="flex-1">
            <label className="block text-amber-200 mb-2">Category</label>
            <div className="relative">
              <select
                className="w-full bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg appearance-none cursor-pointer"
                value={selectedCategory}
                onChange={(e) => setSelectedCategory(e.target.value)}
              >
                <option value="all">All Categories</option>
                <option value="protocols">Protocols</option>
                <option value="troubleshooting">Troubleshooting</option>
                <option value="fundamentals">Fundamentals</option>
                <option value="security">Security</option>
              </select>
              <i className="fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-amber-500"></i>
            </div>
          </div>
          <div className="flex-1">
            <label className="block text-amber-200 mb-2">Difficulty</label>
            <div className="relative">
              <select
                className="w-full bg-[#2a1a05] text-amber-100 px-4 py-2 rounded-lg appearance-none cursor-pointer"
                value={selectedDifficulty}
                onChange={(e) => setSelectedDifficulty(e.target.value)}
              >
                <option value="all">All Levels</option>
                <option value="beginner">Beginner</option>
                <option value="intermediate">Intermediate</option>
                <option value="advanced">Advanced</option>
              </select>
              <i className="fas fa-chevron-down absolute right-4 top-1/2 transform -translate-y-1/2 text-amber-500"></i>
            </div>
          </div>
        </div>

        <div className="bg-[url('https://readdy.ai/api/search-image?query=Antique%20wooden%20bookshelf%20with%20intricate%20carvings%20and%20warm%20ambient%20lighting%2C%20rich%20mahogany%20texture%20with%20mystical%20aura%2C%20vintage%20library%20atmosphere&width=1440&height=900&seq=shelf&orientation=landscape')] bg-cover bg-center p-8 rounded-xl shadow-2xl min-h-[900px] flex items-center">
          <div className="flex gap-2 items-end">
            {allBooks.map(book => (
              <BookSpine key={book.id} book={book} />
            ))}
          </div>
        </div>

        {hoveredBook && (
          <div
            className="fixed top-1/2 left-1/2 transform -translate-x-1/2 -translate-y-1/2 bg-[#1a0f00] p-6 rounded-lg shadow-xl border border-[#3a2a15] max-w-md z-50"
            onClick={(e) => e.stopPropagation()}
          >
            <div className="flex gap-6">
              <img src={hoveredBook.coverImage} alt={hoveredBook.title} className="w-32 h-48 object-cover rounded-lg shadow-lg" />
              <div>
                <h3 className="text-xl font-bold text-amber-100 mb-2">{hoveredBook.title}</h3>
                <p className="text-amber-200 text-sm mb-2">By {hoveredBook.author}</p>
                <p className="text-amber-300 text-sm mb-4">{hoveredBook.description}</p>
                <div className="flex gap-2">
                  <button className="bg-amber-700 text-amber-100 px-4 py-2 text-sm rounded-lg hover:bg-amber-600 transition-colors rounded-button">
                    <i className="fas fa-bookmark mr-2"></i>
                    Bookmark
                  </button>
                  <button
                    onClick={() => {
                      const overlay = document.createElement('div');
                      overlay.className = 'fixed inset-0 bg-black/80 z-[60] flex items-center justify-center';
                      const book = document.createElement('div');
                      book.className = 'w-[300px] h-[400px] relative transform transition-all duration-1000';
                      book.style.perspective = '1000px';
                      const cover = document.createElement('div');
                      cover.className = `absolute inset-0 bg-cover bg-center rounded-r-lg transform-origin-left transition-transform duration-1000`;
                      cover.style.backgroundImage = `url(${hoveredBook.coverImage})`;
                      cover.style.transformStyle = 'preserve-3d';
                      book.appendChild(cover);
                      overlay.appendChild(book);
                      document.body.appendChild(overlay);
                      setTimeout(() => {
                        cover.style.transform = 'rotateY(-180deg)';
                      }, 100);
                      setTimeout(() => {
                        window.location.href = hoveredBook.wikiLink;
                      }, 1000);
                    }}
                    className="bg-green-700 text-green-100 px-4 py-2 text-sm rounded-lg hover:bg-green-600 transition-colors rounded-button"
                  >
                    <i className="fas fa-book-reader mr-2"></i>
                    {hoveredBook.isUnique ? 'View Case Study' : 'Read Article'}
                  </button>
                </div>
              </div>
            </div>
          </div>
        )}
      </main>
    </div>
  );
};

export default LibraryPages;
