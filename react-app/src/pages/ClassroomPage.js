// Converted from ClassroomSplash-Page.tsx - The exported code uses Tailwind CSS
import React, { useState, useEffect, useRef } from 'react';
import { Link } from 'react-router-dom';

const ClassroomPage = ({ user, moodleData }) => {
  const [sideMenuOpen, setSideMenuOpen] = useState(false);
  const [activeTab, setActiveTab] = useState('dashboard');
  const [hoverCourse, setHoverCourse] = useState(null);
  const previewPlayerRef = useRef(null);
  const progressChartRef = useRef(null);

  useEffect(() => {
    // Simplified chart implementation for now
    if (progressChartRef.current) {
      // Chart initialization would go here
      // For now, we'll use a simple implementation
    }
  }, []);

  const toggleSideMenu = () => {
    setSideMenuOpen(!sideMenuOpen);
  };

  const handleCourseHover = (index) => {
    setHoverCourse(index);
    if (previewPlayerRef.current && index !== null) {
      previewPlayerRef.current.play().catch(e => console.log("Autoplay prevented:", e));
    }
  };

  const featuredCourses = [
    {
      id: 1,
      title: "Advanced Network Enchantments",
      instructor: "Professor Elara Waveweaver",
      progress: 65,
      image: "https://readdy.ai/api/search-image?query=Magical%2520network%2520operations%2520center%2520with%2520glowing%2520blue%2520and%2520purple%2520holographic%2520displays%2520showing%2520network%2520traffic%2520visualizations%252C%2520staffed%2520by%2520professionals%2520in%2520tech-inspired%2520robes%252C%2520with%2520floating%2520data%2520streams%2520and%2520ethereal%2520connections%2520between%2520server%2520racks%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course1&orientation=landscape",
      isSeminar: false,
      description: "Master the art of network enchantment with advanced techniques for optimizing magical data flows."
    },
    {
      id: 2,
      title: "Packet Manipulation Arts",
      instructor: "Dr. Thorne Cablemancer",
      progress: 30,
      image: "https://readdy.ai/api/search-image?query=Ethereal%2520hands%2520manipulating%2520glowing%2520network%2520packets%2520floating%2520in%2520air%252C%2520with%2520streams%2520of%2520data%2520flowing%2520between%2520crystalline%2520nodes%252C%2520against%2520a%2520backdrop%2520of%2520magical%2520server%2520infrastructure%2520with%2520blue%2520and%2520purple%2520lighting%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course2&orientation=landscape",
      isSeminar: false,
      description: "Learn to shape and direct network packets with precision and efficiency."
    },
    {
      id: 3,
      title: "Network Defense Against Dark Arts",
      instructor: "Master Orion Signalkeeper",
      progress: 0,
      image: "https://readdy.ai/api/search-image?query=Magical%2520shield%2520protecting%2520network%2520infrastructure%2520from%2520shadowy%2520attacks%252C%2520with%2520glowing%2520blue%2520defensive%2520runes%2520and%2520sigils%252C%2520ethereal%2520guardians%2520monitoring%2520for%2520intrusions%252C%2520dark%2520background%2520with%2520contrasting%2520magical%2520light%2520effects%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=course3&orientation=landscape",
      isSeminar: false,
      description: "Protect your networks from malicious attacks with advanced defensive techniques."
    },
    {
      id: 4,
      title: "Live Seminar: Future of Magical Networks",
      instructor: "Professor Iris Dataweave",
      date: "May 10, 2025 • 2:00 PM",
      image: "https://readdy.ai/api/search-image?query=Futuristic%2520magical%2520auditorium%2520with%2520a%2520presenter%2520on%2520stage%2520demonstrating%2520advanced%2520network%2520concepts%2520with%2520floating%2520holographic%2520displays%252C%2520audience%2520of%2520diverse%2520students%2520in%2520tech-magical%2520attire%252C%2520glowing%2520blue%2520and%2520purple%2520lighting%252C%2520high%2520quality%2520digital%2520art&width=400&height=225&seq=seminar1&orientation=landscape",
      isSeminar: true,
      seats: "43/100 seats available",
      description: "Join us for a live discussion on emerging technologies and future trends in magical networking."
    }
  ];

  return (
    <div className="min-h-screen bg-gray-900 text-white font-sans">
      {/* Side Menu */}
      <div className={`fixed top-0 left-0 h-full w-80 bg-indigo-900 z-50 transform transition-transform duration-300 ease-in-out shadow-2xl ${sideMenuOpen ? 'translate-x-0' : '-translate-x-full'}`}>
        <div className="p-6">
          <div className="flex justify-between items-center mb-8">
            <h2 className="text-2xl font-bold text-white">Student Dashboard</h2>
            <button onClick={toggleSideMenu} className="text-white hover:text-blue-300 cursor-pointer">
              <i className="fa-solid fa-times text-xl"></i>
            </button>
          </div>
          <div className="flex items-center mb-8 border-b border-indigo-700 pb-6">
            <div className="h-16 w-16 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-2xl font-bold">
              SW
            </div>
            <div className="ml-4">
              <h3 className="text-lg font-semibold">Student Wizard</h3>
              <p className="text-blue-300">Network Operations</p>
            </div>
          </div>
          <nav className="mb-8">
            <ul className="space-y-2">
              <li>
                <button
                  onClick={() => setActiveTab('dashboard')}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'dashboard' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`}
                >
                  <i className="fa-solid fa-gauge-high mr-3"></i>
                  Dashboard
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveTab('courses')}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'courses' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`}
                >
                  <i className="fa-solid fa-graduation-cap mr-3"></i>
                  My Courses
                </button>
              </li>
              <li>
                <button
                  onClick={() => setActiveTab('schedule')}
                  className={`w-full text-left px-4 py-3 rounded-lg flex items-center ${activeTab === 'schedule' ? 'bg-indigo-800 text-white' : 'text-blue-200 hover:bg-indigo-800'}`}
                >
                  <i className="fa-solid fa-calendar-alt mr-3"></i>
                  Schedule
                </button>
              </li>
            </ul>
          </nav>
          <div className="border-t border-indigo-700 pt-6">
            <h4 className="text-lg font-semibold mb-4">Course Progress</h4>
            <div ref={progressChartRef} className="h-48 w-full bg-gray-800 rounded-lg flex items-center justify-center">
              <div className="text-center">
                <div className="text-4xl font-bold text-blue-400 mb-2">65%</div>
                <div className="text-sm text-gray-400">Overall Progress</div>
              </div>
            </div>
            <div className="mt-4 grid grid-cols-3 gap-2 text-center text-sm">
              <div>
                <div className="font-semibold">Completed</div>
                <div className="text-blue-300">35%</div>
              </div>
              <div>
                <div className="font-semibold">In Progress</div>
                <div className="text-blue-300">20%</div>
              </div>
              <div>
                <div className="font-semibold">Not Started</div>
                <div className="text-blue-300">45%</div>
              </div>
            </div>
          </div>
          <div className="mt-8">
            <Link
              to="/departments"
              className="block w-full px-4 py-3 bg-gradient-to-r from-purple-600 to-indigo-600 text-white rounded-lg text-center hover:from-purple-700 hover:to-indigo-700 transition-all duration-300 font-medium shadow-md"
            >
              <i className="fa-solid fa-arrow-left mr-2"></i>
              Back to Department
            </Link>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="min-h-screen relative">
        <div className="absolute inset-0 z-0">
          <img
            src="https://readdy.ai/api/search-image?query=Abstract%20digital%20network%20visualization%20with%20glowing%20blue%20and%20purple%20nodes%20connected%20by%20light%20streams%2C%20creating%20an%20intricate%20weaving%20pattern%20across%20a%20dark%20background%2C%20modern%20tech%20aesthetic%20with%20depth%20and%20dimension%2C%20professional%20enterprise%20grade%20network%20visualization&width=1920&height=1080&seq=bg1&orientation=landscape"
            alt="Network Background"
            className="w-full h-full object-cover opacity-10"
          />
        </div>
        <div className="relative z-10">
          {/* Header */}
          <header className="bg-indigo-900/90 shadow-lg backdrop-blur-sm">
            <div className="container mx-auto px-6 py-4">
              <div className="flex justify-between items-center">
                <div className="flex items-center">
                  <button onClick={toggleSideMenu} className="mr-4 text-white hover:text-blue-300 cursor-pointer">
                    <i className="fa-solid fa-bars text-xl"></i>
                  </button>
                  <h1 className="text-2xl font-bold text-white">Network Operations Classroom</h1>
                </div>
                <div className="flex items-center space-x-4">
                  <div className="relative">
                    <input
                      type="text"
                      placeholder="Search courses..."
                      className="px-4 py-2 bg-indigo-800 bg-opacity-50 rounded-lg text-white placeholder-blue-300 border-none focus:outline-none focus:ring-2 focus:ring-blue-500 w-64 text-sm"
                    />
                    <i className="fa-solid fa-search absolute right-3 top-1/2 transform -translate-y-1/2 text-blue-300"></i>
                  </div>
                  <button className="relative text-white hover:text-blue-300">
                    <i className="fa-solid fa-bell text-xl"></i>
                    <span className="absolute -top-1 -right-1 h-4 w-4 bg-red-500 rounded-full text-xs flex items-center justify-center">2</span>
                  </button>
                  <div className="h-10 w-10 rounded-full bg-gradient-to-r from-blue-500 to-purple-600 flex items-center justify-center text-lg font-bold cursor-pointer">
                    SW
                  </div>
                </div>
              </div>
            </div>
          </header>

          {/* Featured Course Preview with Video */}
          <div className="relative h-[70vh] overflow-hidden">
            <div className="absolute inset-0 bg-gradient-to-b from-indigo-900/90 to-gray-900/90 backdrop-blur-sm"></div>
            {hoverCourse !== null && (
              <div className="absolute inset-0 z-0">
                <div className="absolute inset-0 bg-gradient-to-r from-indigo-900 via-indigo-900/80 to-transparent z-10"></div>
                <video
                  ref={previewPlayerRef}
                  className="w-full h-full object-cover opacity-50"
                  loop
                  muted
                  playsInline
                >
                  <source src="https://assets.mixkit.co/videos/preview/mixkit-digital-network-connection-over-blue-background-97.mp4" type="video/mp4" />
                </video>
              </div>
            )}
            <div className="absolute inset-0 flex items-center z-10">
              <div className="container mx-auto px-6">
                <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
                  <div>
                    {hoverCourse !== null ? (
                      <>
                        <div className="mb-2 text-blue-400 font-medium">
                          {featuredCourses[hoverCourse].isSeminar ? 'LIVE SEMINAR' : 'FEATURED COURSE'}
                        </div>
                        <h2 className="text-4xl font-bold mb-4">{featuredCourses[hoverCourse].title}</h2>
                        <p className="text-blue-200 mb-2">
                          <i className="fa-solid fa-user-tie mr-2"></i>
                          {featuredCourses[hoverCourse].instructor}
                        </p>
                        {featuredCourses[hoverCourse].isSeminar ? (
                          <p className="text-blue-200 mb-4">
                            <i className="fa-solid fa-calendar-day mr-2"></i>
                            {featuredCourses[hoverCourse].date}
                          </p>
                        ) : (
                          <div className="mb-4">
                            <div className="flex items-center mb-1">
                              <div className="text-sm text-blue-200 mr-2">Progress:</div>
                              <div className="text-sm text-blue-200">{featuredCourses[hoverCourse].progress}%</div>
                            </div>
                            <div className="w-full bg-gray-700 rounded-full h-2">
                              <div
                                className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                                style={{ width: `${featuredCourses[hoverCourse].progress}%` }}
                              ></div>
                            </div>
                          </div>
                        )}
                        <p className="text-lg text-gray-300 mb-6">
                          {featuredCourses[hoverCourse].description}
                        </p>
                        <div className="flex flex-col sm:flex-row gap-4">
                          <Link
                            to="/learning"
                            className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg text-center"
                          >
                            {featuredCourses[hoverCourse].isSeminar ? 'Register for Seminar' :
                             featuredCourses[hoverCourse].progress > 0 ? 'Continue Learning' : 'Start Course'}
                          </Link>
                          <button className="px-6 py-3 bg-gray-800 rounded-lg hover:bg-gray-700 transition-all duration-300 font-medium">
                            View Details
                          </button>
                        </div>
                      </>
                    ) : (
                      <>
                        <h2 className="text-4xl font-bold mb-4">Welcome to the Classroom</h2>
                        <p className="text-lg text-gray-300 mb-6">
                          Explore your courses, track your progress, and enhance your network operations skills. Hover over a course below to see more details.
                        </p>
                        <button className="px-6 py-3 bg-gradient-to-r from-purple-600 to-blue-600 rounded-lg hover:from-purple-700 hover:to-blue-700 transition-all duration-300 font-medium shadow-lg">
                          Browse All Courses
                        </button>
                      </>
                    )}
                  </div>
                </div>
              </div>
            </div>
          </div>

          {/* Dynamic Course Categories */}
          <div className="container mx-auto px-6 py-12">
            {/* Continue Learning Section */}
            <div className="mb-16">
              <h2 className="text-2xl font-bold mb-6">Continue Learning</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {featuredCourses.filter(course => !course.isSeminar).map((course, index) => (
                  <div
                    key={course.id}
                    className="bg-gray-800 rounded-xl overflow-hidden shadow-lg hover:shadow-blue-500/10 transition-all duration-300 cursor-pointer h-full"
                    onMouseEnter={() => handleCourseHover(index)}
                    onMouseLeave={() => handleCourseHover(null)}
                  >
                    <div className="h-48 overflow-hidden">
                      <img
                        src={course.image}
                        alt={course.title}
                        className="w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-6">
                      <h3 className="text-xl font-bold mb-2">{course.title}</h3>
                      <p className="text-blue-300 text-sm mb-4">
                        <i className="fa-solid fa-user-tie mr-2"></i>
                        {course.instructor}
                      </p>
                      <div className="mb-4">
                        <div className="flex justify-between text-sm mb-1">
                          <span>Progress</span>
                          <span>{course.progress}%</span>
                        </div>
                        <div className="w-full bg-gray-700 rounded-full h-2">
                          <div
                            className="bg-gradient-to-r from-blue-500 to-purple-600 h-2 rounded-full"
                            style={{ width: `${course.progress}%` }}
                          ></div>
                        </div>
                      </div>
                      <div className="flex justify-between items-center">
                        <Link
                          to="/learning"
                          className="px-4 py-2 bg-blue-600 text-white rounded-lg hover:bg-blue-700 transition-colors duration-300 font-medium text-center"
                        >
                          {course.progress > 0 ? 'Continue' : 'Start'}
                        </Link>
                        <button className="px-4 py-2 bg-gray-700 text-white rounded-lg hover:bg-gray-600 transition-colors duration-300 font-medium">
                          Details
                        </button>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* Live Seminars Section */}
            <div className="mb-16">
              <h2 className="text-2xl font-bold mb-6">Live Seminars</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {featuredCourses.filter(course => course.isSeminar).map((course, index) => (
                  <div
                    key={course.id}
                    className="bg-gradient-to-r from-purple-900 to-indigo-900 rounded-xl overflow-hidden shadow-lg hover:shadow-purple-500/20 transition-all duration-300 cursor-pointer"
                    onMouseEnter={() => handleCourseHover(featuredCourses.indexOf(course))}
                    onMouseLeave={() => handleCourseHover(null)}
                  >
                    <div className="h-48 overflow-hidden">
                      <img
                        src={course.image}
                        alt={course.title}
                        className="w-full h-full object-cover object-top transform hover:scale-105 transition-transform duration-300"
                      />
                    </div>
                    <div className="p-6">
                      <div className="flex items-center mb-2">
                        <span className="bg-red-500 text-white text-xs px-2 py-1 rounded-full mr-2">LIVE</span>
                        <span className="text-purple-300 text-sm">{course.date}</span>
                      </div>
                      <h3 className="text-xl font-bold mb-2">{course.title}</h3>
                      <p className="text-blue-300 text-sm mb-4">
                        <i className="fa-solid fa-user-tie mr-2"></i>
                        {course.instructor}
                      </p>
                      <p className="text-gray-300 text-sm mb-4">{course.description}</p>
                      <div className="flex justify-between items-center">
                        <Link
                          to="/learning"
                          className="px-4 py-2 bg-purple-600 text-white rounded-lg hover:bg-purple-700 transition-colors duration-300 font-medium text-center"
                        >
                          Register
                        </Link>
                        <span className="text-purple-300 text-sm">
                          <i className="fa-solid fa-users mr-1"></i>
                          {course.seats}
                        </span>
                      </div>
                    </div>
                  </div>
                ))}
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default ClassroomPage;
