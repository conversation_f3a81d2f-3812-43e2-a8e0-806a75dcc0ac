import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

const ClassroomPage = ({ user, moodleData }) => {
  const [activeTab, setActiveTab] = useState('dashboard');
  const [isScrolled, setIsScrolled] = useState(false);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // Mock data for demonstration
  const mockUser = user || {
    firstname: 'Apprentice',
    lastname: 'Technomancer',
    department: 'Network Weavers',
    level: 'Intermediate',
    progress: 65
  };

  const mockCourses = [
    {
      id: 1,
      title: 'Fundamentals of Magical Networking',
      progress: 85,
      nextLesson: 'Advanced Routing Spells',
      instructor: 'Professor <PERSON><PERSON>',
      difficulty: 'Beginner',
      timeRemaining: '2 weeks'
    },
    {
      id: 2,
      title: 'Fiber Optic Enchantments',
      progress: 60,
      nextLesson: 'Light Manipulation Techniques',
      instructor: 'Master Luminos',
      difficulty: 'Intermediate',
      timeRemaining: '1 month'
    },
    {
      id: 3,
      title: 'Network Security Wards',
      progress: 30,
      nextLesson: 'Firewall Incantations',
      instructor: 'Guardian Shieldmaster',
      difficulty: 'Advanced',
      timeRemaining: '6 weeks'
    }
  ];

  const mockAchievements = [
    { title: 'First Connection', description: 'Successfully established your first magical network link', earned: true },
    { title: 'Speed of Light', description: 'Achieved maximum data transmission speed', earned: true },
    { title: 'Network Guardian', description: 'Protected a network from dark magic attacks', earned: false },
    { title: 'Master Weaver', description: 'Created a complex multi-node network', earned: false }
  ];

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0500] via-[#1a0f00] to-[#2a1a05] text-white pt-20">
      {/* Hero Section */}
      <section className="relative py-16 overflow-hidden">
        <div className="absolute inset-0 bg-gradient-to-r from-[#15a7dd]/20 to-[#6a3293]/20"></div>
        <div className="container mx-auto px-6 relative z-10">
          <div className="text-center mb-12">
            <h1 className="text-4xl md:text-5xl font-serif font-bold mb-4">
              Welcome to Your <span className="text-[#15a7dd]">Magical Classroom</span>
            </h1>
            <p className="text-xl text-gray-300 max-w-3xl mx-auto">
              Your personalized learning sanctuary where ancient wisdom meets modern technology. 
              Track your progress, access your courses, and continue your journey to becoming a master technomancer.
            </p>
          </div>

          {/* User Stats */}
          <div className="grid grid-cols-1 md:grid-cols-4 gap-6 mb-12">
            <div className="bg-gradient-to-br from-[#15a7dd]/20 to-[#15a7dd]/10 rounded-lg p-6 border border-[#15a7dd]/30">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Overall Progress</h3>
                <i className="fas fa-chart-line text-[#15a7dd] text-xl"></i>
              </div>
              <div className="text-3xl font-bold text-[#15a7dd] mb-2">{mockUser.progress}%</div>
              <div className="w-full bg-gray-700 rounded-full h-2">
                <div className="bg-[#15a7dd] h-2 rounded-full" style={{ width: `${mockUser.progress}%` }}></div>
              </div>
            </div>

            <div className="bg-gradient-to-br from-[#6a3293]/20 to-[#6a3293]/10 rounded-lg p-6 border border-[#6a3293]/30">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Active Courses</h3>
                <i className="fas fa-book-open text-[#6a3293] text-xl"></i>
              </div>
              <div className="text-3xl font-bold text-[#6a3293] mb-2">{mockCourses.length}</div>
              <p className="text-gray-400 text-sm">Currently enrolled</p>
            </div>

            <div className="bg-gradient-to-br from-yellow-500/20 to-yellow-500/10 rounded-lg p-6 border border-yellow-500/30">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Achievements</h3>
                <i className="fas fa-trophy text-yellow-500 text-xl"></i>
              </div>
              <div className="text-3xl font-bold text-yellow-500 mb-2">
                {mockAchievements.filter(a => a.earned).length}/{mockAchievements.length}
              </div>
              <p className="text-gray-400 text-sm">Unlocked</p>
            </div>

            <div className="bg-gradient-to-br from-green-500/20 to-green-500/10 rounded-lg p-6 border border-green-500/30">
              <div className="flex items-center justify-between mb-4">
                <h3 className="text-lg font-semibold">Department</h3>
                <i className="fas fa-users text-green-500 text-xl"></i>
              </div>
              <div className="text-lg font-bold text-green-500 mb-2">{mockUser.department}</div>
              <p className="text-gray-400 text-sm">{mockUser.level} Level</p>
            </div>
          </div>
        </div>
      </section>

      {/* Main Content */}
      <section className="py-12">
        <div className="container mx-auto px-6">
          {/* Tabs */}
          <div className="flex flex-wrap border-b border-gray-700 mb-8">
            {['dashboard', 'courses', 'achievements', 'schedule'].map((tab) => (
              <button
                key={tab}
                onClick={() => setActiveTab(tab)}
                className={`px-6 py-3 font-medium text-sm transition-colors duration-300 rounded-button whitespace-nowrap cursor-pointer ${
                  activeTab === tab
                    ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]'
                    : 'text-gray-400 hover:text-[#15a7dd]'
                }`}
              >
                {tab.charAt(0).toUpperCase() + tab.slice(1)}
              </button>
            ))}
          </div>

          {/* Dashboard Tab */}
          {activeTab === 'dashboard' && (
            <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
              <div className="lg:col-span-2">
                <h2 className="text-2xl font-serif font-bold mb-6">Recent Activity</h2>
                <div className="space-y-4">
                  {[
                    { action: 'Completed lesson', course: 'Fundamentals of Magical Networking', time: '2 hours ago' },
                    { action: 'Started new course', course: 'Network Security Wards', time: '1 day ago' },
                    { action: 'Earned achievement', course: 'Speed of Light', time: '3 days ago' },
                    { action: 'Submitted assignment', course: 'Fiber Optic Enchantments', time: '1 week ago' }
                  ].map((activity, index) => (
                    <div key={index} className="bg-[#1a0f00] rounded-lg p-4 border border-gray-700">
                      <div className="flex items-center justify-between">
                        <div>
                          <p className="font-medium">{activity.action}</p>
                          <p className="text-[#15a7dd] text-sm">{activity.course}</p>
                        </div>
                        <span className="text-gray-400 text-sm">{activity.time}</span>
                      </div>
                    </div>
                  ))}
                </div>
              </div>

              <div>
                <h2 className="text-2xl font-serif font-bold mb-6">Quick Actions</h2>
                <div className="space-y-4">
                  <Link to="/learning" className="block w-full p-4 bg-[#15a7dd] rounded-lg hover:bg-[#1397c7] transition-colors duration-300">
                    <div className="flex items-center">
                      <i className="fas fa-play mr-3 text-xl"></i>
                      <div>
                        <h3 className="font-semibold">Continue Learning</h3>
                        <p className="text-sm opacity-90">Resume your current course</p>
                      </div>
                    </div>
                  </Link>

                  <Link to="/library" className="block w-full p-4 bg-[#6a3293] rounded-lg hover:bg-[#5a2283] transition-colors duration-300">
                    <div className="flex items-center">
                      <i className="fas fa-book mr-3 text-xl"></i>
                      <div>
                        <h3 className="font-semibold">Visit Library</h3>
                        <p className="text-sm opacity-90">Access learning resources</p>
                      </div>
                    </div>
                  </Link>

                  <button className="block w-full p-4 bg-yellow-600 rounded-lg hover:bg-yellow-700 transition-colors duration-300">
                    <div className="flex items-center">
                      <i className="fas fa-calendar mr-3 text-xl"></i>
                      <div>
                        <h3 className="font-semibold">Schedule Study</h3>
                        <p className="text-sm opacity-90">Plan your learning time</p>
                      </div>
                    </div>
                  </button>
                </div>
              </div>
            </div>
          )}

          {/* Courses Tab */}
          {activeTab === 'courses' && (
            <div>
              <h2 className="text-2xl font-serif font-bold mb-6">Your Courses</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                {mockCourses.map((course) => (
                  <div key={course.id} className="bg-[#1a0f00] rounded-lg p-6 border border-gray-700 hover:border-[#15a7dd]/50 transition-colors duration-300">
                    <h3 className="text-xl font-semibold mb-2">{course.title}</h3>
                    <p className="text-gray-400 mb-4">Instructor: {course.instructor}</p>
                    
                    <div className="mb-4">
                      <div className="flex justify-between text-sm mb-2">
                        <span>Progress</span>
                        <span>{course.progress}%</span>
                      </div>
                      <div className="w-full bg-gray-700 rounded-full h-2">
                        <div className="bg-[#15a7dd] h-2 rounded-full" style={{ width: `${course.progress}%` }}></div>
                      </div>
                    </div>

                    <div className="space-y-2 mb-4">
                      <p className="text-sm"><span className="text-gray-400">Next:</span> {course.nextLesson}</p>
                      <p className="text-sm"><span className="text-gray-400">Time remaining:</span> {course.timeRemaining}</p>
                      <span className={`inline-block px-2 py-1 rounded text-xs ${
                        course.difficulty === 'Beginner' ? 'bg-green-600' :
                        course.difficulty === 'Intermediate' ? 'bg-yellow-600' : 'bg-red-600'
                      }`}>
                        {course.difficulty}
                      </span>
                    </div>

                    <Link to="/learning" className="block w-full text-center py-2 bg-[#15a7dd] rounded hover:bg-[#1397c7] transition-colors duration-300">
                      Continue Course
                    </Link>
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Achievements Tab */}
          {activeTab === 'achievements' && (
            <div>
              <h2 className="text-2xl font-serif font-bold mb-6">Achievements</h2>
              <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
                {mockAchievements.map((achievement, index) => (
                  <div key={index} className={`rounded-lg p-6 border ${
                    achievement.earned 
                      ? 'bg-gradient-to-br from-yellow-500/20 to-yellow-500/10 border-yellow-500/30' 
                      : 'bg-[#1a0f00] border-gray-700'
                  }`}>
                    <div className="flex items-center mb-4">
                      <i className={`fas fa-trophy text-2xl mr-4 ${
                        achievement.earned ? 'text-yellow-500' : 'text-gray-600'
                      }`}></i>
                      <div>
                        <h3 className="text-lg font-semibold">{achievement.title}</h3>
                        <p className={`text-sm ${achievement.earned ? 'text-yellow-200' : 'text-gray-400'}`}>
                          {achievement.description}
                        </p>
                      </div>
                    </div>
                    {achievement.earned && (
                      <span className="inline-block px-3 py-1 bg-yellow-500 text-black text-xs rounded-full font-medium">
                        Earned
                      </span>
                    )}
                  </div>
                ))}
              </div>
            </div>
          )}

          {/* Schedule Tab */}
          {activeTab === 'schedule' && (
            <div>
              <h2 className="text-2xl font-serif font-bold mb-6">Study Schedule</h2>
              <div className="bg-[#1a0f00] rounded-lg p-6 border border-gray-700">
                <p className="text-center text-gray-400 py-12">
                  <i className="fas fa-calendar-plus text-4xl mb-4 block"></i>
                  Schedule feature coming soon! Plan your magical studies with our upcoming scheduling system.
                </p>
              </div>
            </div>
          )}
        </div>
      </section>
    </div>
  );
};

export default ClassroomPage;
