import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

const FinancialDeptPage = () => {
  const [activeTab, setActiveTab] = useState('overview');
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  const [showFacultyDetails, setShowFacultyDetails] = useState(null);

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    
    // Particle animation
    const createParticles = () => {
      const particleContainer = document.getElementById('particle-container');
      if (!particleContainer) return;
      
      for (let i = 0; i < 30; i++) {
        const particle = document.createElement('div');
        particle.className = 'absolute w-1 h-1 rounded-full bg-yellow-400 opacity-0';
        // Random position
        particle.style.left = `${Math.random() * 100}%`;
        particle.style.top = `${Math.random() * 100}%`;
        // Random animation duration
        const duration = 3 + Math.random() * 5;
        particle.style.animation = `float ${duration}s ease-in-out infinite`;
        particle.style.animationDelay = `${Math.random() * 5}s`;
        particleContainer.appendChild(particle);
      }
    };
    
    createParticles();
    
    return () => {
      window.removeEventListener('scroll', handleScroll);
    };
  }, []);

  const facultyMembers = [
    {
      name: "Professor Goldstein",
      title: "Gold Transmutation Master",
      experience: "45 years",
      achievements: "Discovered the Golden Ratio Spell, Author of 'Modern Alchemical Finance'",
      image: "https://readdy.ai/api/search-image?query=A%20distinguished%20elderly%20wizard%20professor%20with%20a%20neatly%20trimmed%20white%20beard%20and%20gold-rimmed%20spectacles&width=300&height=300&seq=prof1&orientation=squarish"
    },
    {
      name: "Dr. Silverton",
      title: "Precious Metals Expert",
      experience: "38 years",
      achievements: "Created the Silver Stream Investment Method, Former Royal Treasury Advisor",
      image: "https://readdy.ai/api/search-image?query=A%20middle-aged%20female%20professor%20with%20silver-streaked%20hair%20in%20an%20elegant%20updo&width=300&height=300&seq=prof2&orientation=squarish"
    },
    {
      name: "Master Bronzewing",
      title: "Risk Assessment Specialist",
      experience: "29 years",
      achievements: "Developed the Bronze Shield Protection Spell, Led the Great Market Stabilization of 2018",
      image: "https://readdy.ai/api/search-image?query=A%20confident%20wizard%20in%20his%20thirties%20with%20short%20bronze-colored%20hair%20and%20a%20well-groomed%20beard&width=300&height=300&seq=prof3&orientation=squarish"
    },
    {
      name: "Lady Platina",
      title: "Investment Strategy Archmage",
      experience: "42 years",
      achievements: "Inventor of the Platinum Growth Portfolio Spell, Five-time winner of the Golden Cauldron Award",
      image: "https://readdy.ai/api/search-image?query=An%20elegant%20older%20woman%20with%20platinum%20blonde%20hair%20in%20a%20sophisticated%20style&width=300&height=300&seq=prof4&orientation=squarish"
    }
  ];

  const programs = [
    {
      title: "Magical Investment Management",
      description: "Learn to harness magical energies to identify investment opportunities and maximize returns through alchemical transformations.",
      icon: "fa-chart-line"
    },
    {
      title: "Alchemical Trading",
      description: "Master the ancient art of transforming base metals into gold while applying modern trading strategies to magical markets.",
      icon: "fa-exchange-alt"
    },
    {
      title: "Mystical Risk Assessment",
      description: "Develop your third eye to foresee market fluctuations and create protective wards against financial losses.",
      icon: "fa-shield-alt"
    },
    {
      title: "Enchanted Portfolio Management",
      description: "Craft balanced portfolios using divination techniques and magical asset allocation to achieve long-term prosperity.",
      icon: "fa-wallet"
    }
  ];

  return (
    <div className="min-h-screen bg-white text-gray-800 font-sans pt-20">
      {/* Particle container for magical effects */}
      <div id="particle-container" className="fixed inset-0 pointer-events-none z-0"></div>
      
      {/* Breadcrumb */}
      <div className="bg-gray-100 py-4">
        <div className="container mx-auto px-6">
          <div className="flex items-center text-sm text-gray-600">
            <Link to="/" className="hover:text-[#15a7dd] transition-colors duration-300 cursor-pointer">Home</Link>
            <i className="fas fa-chevron-right mx-2 text-xs text-gray-400"></i>
            <Link to="/departments" className="hover:text-[#15a7dd] transition-colors duration-300 cursor-pointer">Departments</Link>
            <i className="fas fa-chevron-right mx-2 text-xs text-gray-400"></i>
            <span className="text-[#15a7dd]">Financial Department (Alchemists)</span>
          </div>
        </div>
      </div>

      {/* Hero Banner */}
      <div className="relative h-[50vh] min-h-[600px] overflow-hidden">
        <img
          src="https://readdy.ai/api/search-image?query=A%20magical%20financial%20department%20with%20floating%20gold%20coins%20and%20magical%20ledgers.%20Wizards%20in%20elegant%20robes%20with%20gold%20trim%20work%20at%20enchanted%20desks&width=1440&height=800&seq=herobanner&orientation=landscape"
          alt="Financial Department"
          className="w-full h-full object-cover"
        />
        <div className="absolute inset-0 bg-gradient-to-r from-black/70 to-transparent"></div>
        <div className="absolute inset-0 bg-gradient-to-b from-transparent via-transparent to-white/90"></div>
        <div className="absolute inset-0 flex items-center">
          <div className="container mx-auto px-6">
            <h1 className="text-5xl md:text-6xl lg:text-7xl font-serif font-bold text-white mb-6">
              Financial Department <span className="bg-clip-text text-transparent bg-gradient-to-r from-yellow-400 to-yellow-600">(Alchemists)</span>
            </h1>
            <p className="text-xl md:text-2xl text-white max-w-3xl leading-relaxed">
              Where financial expertise meets magical alchemy to transform resources into prosperity through ancient arts and modern techniques.
            </p>
          </div>
        </div>
      </div>

      {/* Main Content */}
      <div className="container mx-auto px-6 py-12">
        {/* Tabs Navigation */}
        <div className="flex flex-wrap border-b border-gray-200 mb-12">
          {['overview', 'programs', 'faculty', 'careers'].map((tab) => (
            <button
              key={tab}
              onClick={() => setActiveTab(tab)}
              className={`px-6 py-3 font-medium text-sm transition-colors duration-300 rounded-button whitespace-nowrap cursor-pointer ${
                activeTab === tab
                  ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]'
                  : 'text-gray-500 hover:text-[#15a7dd]'
              }`}
            >
              {tab.charAt(0).toUpperCase() + tab.slice(1)}
            </button>
          ))}
        </div>

        {/* Overview Section */}
        {activeTab === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-12">
            <div className="lg:col-span-2">
              <h2 className="text-3xl font-serif font-bold mb-6 text-gray-800">Department Overview</h2>
              <p className="text-gray-700 mb-6 leading-relaxed">
                The Financial Department, known colloquially as the Alchemists, represents the perfect blend of ancient magical wisdom and cutting-edge financial expertise. Our department trains students in the delicate art of transforming raw resources into financial prosperity through a combination of traditional alchemical practices and modern financial theory.
              </p>
              <p className="text-gray-700 mb-6 leading-relaxed">
                Founded by Master Goldstein in 1823, our department has a long tradition of excellence in magical finance. We pride ourselves on maintaining the highest ethical standards while teaching students to harness the power of financial alchemy for the greater good of the magical community.
              </p>
              
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md">
                  <div className="text-yellow-600 mb-2">
                    <i className="fas fa-coins text-3xl"></i>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">98%</h3>
                  <p className="text-gray-600">Successful Financial Transformations</p>
                </div>
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md">
                  <div className="text-yellow-600 mb-2">
                    <i className="fas fa-exchange-alt text-3xl"></i>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">1:42</h3>
                  <p className="text-gray-600">Gold-to-Value Conversion Rate</p>
                </div>
                <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md">
                  <div className="text-yellow-600 mb-2">
                    <i className="fas fa-user-graduate text-3xl"></i>
                  </div>
                  <h3 className="text-xl font-bold text-gray-800 mb-2">96%</h3>
                  <p className="text-gray-600">Graduate Placement Rate</p>
                </div>
              </div>
            </div>

            <div className="lg:col-span-1">
              <div className="bg-gradient-to-br from-yellow-50 to-yellow-100 rounded-lg p-6 shadow-md mb-8">
                <h3 className="text-xl font-serif font-bold mb-4 text-gray-800">Department Philosophy</h3>
                <div className="mb-6 flex justify-center">
                  <div className="w-32 h-32 rounded-full bg-gradient-to-br from-yellow-300 to-yellow-500 flex items-center justify-center">
                    <img
                      src="https://readdy.ai/api/search-image?query=An%20alchemical%20symbol%20representing%20financial%20transformation&width=200&height=200&seq=symbol&orientation=squarish"
                      alt="Alchemical Symbol"
                      className="w-24 h-24 object-contain"
                    />
                  </div>
                </div>
                <p className="text-gray-700 mb-4">
                  "Through the perfect balance of ancient wisdom and modern innovation, we transform not just metals, but minds and markets."
                </p>
                <p className="text-gray-600 italic text-sm">
                  - Founding principle established by Master Goldstein, 1823
                </p>
              </div>

              <div className="bg-white rounded-lg p-6 shadow-md border border-gray-200">
                <h3 className="text-xl font-serif font-bold mb-4 text-gray-800">Quick Actions</h3>
                <div className="space-y-3">
                  <Link to="/library" className="w-full py-3 bg-gradient-to-r from-yellow-400 to-yellow-600 text-white rounded-full hover:from-yellow-500 hover:to-yellow-700 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer">
                    <i className="fas fa-book-reader mr-2"></i>
                    Visit Library
                  </Link>
                  <Link to="/classroom" className="w-full py-3 border-2 border-yellow-600 text-yellow-600 rounded-full hover:bg-yellow-50 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer">
                    <i className="fas fa-chalkboard-teacher mr-2"></i>
                    Enter Classroom
                  </Link>
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Programs Section */}
        {activeTab === 'programs' && (
          <div>
            <h2 className="text-3xl font-serif font-bold mb-8 text-gray-800">Specialized Programs</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 gap-8">
              {programs.map((program, index) => (
                <div key={index} className="bg-white rounded-lg overflow-hidden shadow-lg border border-yellow-100 hover:shadow-xl transition-all duration-300 group">
                  <div className="bg-gradient-to-r from-yellow-400 to-yellow-600 h-3"></div>
                  <div className="p-8">
                    <div className="w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mb-6">
                      <i className={`fas ${program.icon} text-yellow-600 text-2xl`}></i>
                    </div>
                    <h3 className="text-2xl font-serif font-bold mb-4 text-gray-800 group-hover:text-yellow-600 transition-colors duration-300">
                      {program.title}
                    </h3>
                    <p className="text-gray-700 mb-6">
                      {program.description}
                    </p>
                    <button className="px-6 py-2 border border-yellow-600 text-yellow-600 rounded-full hover:bg-yellow-600 hover:text-white transition-all duration-300 flex items-center rounded-button whitespace-nowrap cursor-pointer">
                      <span>View Curriculum</span>
                      <i className="fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1"></i>
                    </button>
                  </div>
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Faculty Section */}
        {activeTab === 'faculty' && (
          <div>
            <h2 className="text-3xl font-serif font-bold mb-8 text-gray-800">Distinguished Faculty</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-8">
              {facultyMembers.map((faculty, index) => (
                <div
                  key={index}
                  className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 cursor-pointer"
                  onClick={() => setShowFacultyDetails(showFacultyDetails === index ? null : index)}
                >
                  <div className="relative">
                    <img
                      src={faculty.image}
                      alt={faculty.name}
                      className="w-full h-64 object-cover object-top"
                    />
                    <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                    <div className="absolute bottom-0 left-0 right-0 p-4">
                      <h3 className="text-xl font-serif font-bold text-white">{faculty.name}</h3>
                      <p className="text-yellow-300">{faculty.title}</p>
                    </div>
                    <div className="absolute top-4 right-4 w-16 h-16 rounded-full border-4 border-white bg-gradient-to-br from-yellow-400 to-yellow-600 flex items-center justify-center">
                      <span className="text-white font-bold text-sm">{faculty.experience}</span>
                    </div>
                  </div>
                  {showFacultyDetails === index && (
                    <div className="p-6 bg-gray-50">
                      <h4 className="font-bold text-gray-800 mb-2">Notable Achievements:</h4>
                      <p className="text-gray-700 text-sm">{faculty.achievements}</p>
                    </div>
                  )}
                </div>
              ))}
            </div>
          </div>
        )}

        {/* Careers Section */}
        {activeTab === 'careers' && (
          <div>
            <h2 className="text-3xl font-serif font-bold mb-8 text-gray-800">Career Opportunities</h2>
            <div className="grid grid-cols-1 md:grid-cols-3 gap-8">
              {[
                {
                  title: "Magical Investment Banks",
                  positions: ["Junior Gold Transmuter", "Market Divination Analyst", "Wealth Management Sorcerer"],
                  icon: "fa-landmark"
                },
                {
                  title: "Alchemical Trading Firms",
                  positions: ["Metals Transformation Specialist", "Magical Commodities Trader", "Alchemical Algorithm Developer"],
                  icon: "fa-exchange-alt"
                },
                {
                  title: "Mystical Financial Consultancies",
                  positions: ["Financial Forecast Diviner", "Wealth Protection Warder", "Prosperity Spell Consultant"],
                  icon: "fa-hand-holding-usd"
                }
              ].map((career, index) => (
                <div key={index} className="bg-white rounded-lg p-6 shadow-lg border border-yellow-100">
                  <div className="w-16 h-16 rounded-full bg-yellow-100 flex items-center justify-center mb-6">
                    <i className={`fas ${career.icon} text-yellow-600 text-2xl`}></i>
                  </div>
                  <h3 className="text-xl font-serif font-bold mb-4 text-gray-800">{career.title}</h3>
                  <ul className="space-y-2">
                    {career.positions.map((position, idx) => (
                      <li key={idx} className="text-gray-700 text-sm flex items-center">
                        <i className="fas fa-star text-yellow-500 mr-2 text-xs"></i>
                        {position}
                      </li>
                    ))}
                  </ul>
                </div>
              ))}
            </div>
          </div>
        )}
      </div>
    </div>
  );
};

export default FinancialDeptPage;
