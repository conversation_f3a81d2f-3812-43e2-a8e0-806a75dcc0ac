import React from 'react';
import { Link } from 'react-router-dom';

const LeadershipTeamPage = () => {
  return (
    <div className="min-h-screen bg-white text-gray-800 font-sans pt-20">
      <div className="container mx-auto px-6 py-12">
        <div className="text-center mb-12">
          <h1 className="text-4xl md:text-5xl font-serif font-bold mb-6">
            Leadership Team <span className="text-red-600">(Council of Archmages)</span>
          </h1>
          <p className="text-xl text-gray-600 max-w-3xl mx-auto">
            The guiding council of master technomancers who oversee the strategic direction of our mystical institution.
          </p>
        </div>
        
        <div className="bg-red-50 rounded-lg p-8 text-center">
          <i className="fas fa-crown text-6xl text-red-600 mb-4"></i>
          <h2 className="text-2xl font-bold text-gray-800 mb-4">Department Page Coming Soon</h2>
          <p className="text-gray-600 mb-6">
            The Council of Archmages is currently deliberating on the mystical content for this page.
          </p>
          <Link to="/departments" className="inline-flex items-center px-6 py-3 bg-red-600 text-white rounded-full hover:bg-red-700 transition-colors duration-300">
            <i className="fas fa-arrow-left mr-2"></i>
            Back to Departments
          </Link>
        </div>
      </div>
    </div>
  );
};

export default LeadershipTeamPage;
