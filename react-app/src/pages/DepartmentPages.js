import React, { useState, useEffect } from 'react';
import { useParams, Link } from 'react-router-dom';

// Import department page components
import FinancialDeptPage from './departments/FinancialDeptPage';
import ConstructionDeptPage from './departments/ConstructionDeptPage';
import TowerTechniciansDeptPage from './departments/TowerTechniciansDeptPage';
import HumanRelationsDeptPage from './departments/HumanRelationsDeptPage';
import SalesDeptPage from './departments/SalesDeptPage';
import NetworkOperationsDeptPage from './departments/NetworkOperationsDeptPage';
import LeadershipTeamPage from './departments/LeadershipTeamPage';

const DepartmentPages = ({ deptType }) => {
  const { deptName } = useParams();
  const [activeTab, setActiveTab] = useState('overview');
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);

  // Determine which department to show
  const currentDept = deptType || deptName;

  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);

  // If no specific department, show department overview
  if (!currentDept) {
    return (
      <div className="min-h-screen bg-white text-gray-800 font-sans pt-20">
        <div className="container mx-auto px-6 py-12">
          <div className="text-center mb-16">
            <h1 className="text-4xl md:text-5xl font-serif font-bold mb-6">
              <span className="text-[#15a7dd]">Mystical</span> Departments
            </h1>
            <p className="text-[#475467] max-w-3xl mx-auto text-lg">
              Explore our specialized schools of technomancy, each focusing on a different aspect of the magical fiber arts. 
              Choose a department below to learn more about their programs, faculty, and opportunities.
            </p>
            <div className="w-20 h-1 bg-[#15a7dd] mx-auto mt-6"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Finance Alchemy",
                icon: "fa-coins",
                route: "/FinanceDept",
                color: "yellow",
                image: "https://readdy.ai/api/search-image?query=A%20mystical%20treasury%20room%20with%20floating%20gold%20coins%20and%20glowing%20blue%20financial%20charts&width=400&height=300&seq=dept1&orientation=landscape",
                description: "Transform raw resources into financial prosperity through the ancient arts of budget management and revenue enchantment."
              },
              {
                title: "Tower Levitation",
                icon: "fa-tower-broadcast",
                route: "/TowerTechnicians",
                color: "blue",
                image: "https://readdy.ai/api/search-image?query=Magical%20technicians%20working%20on%20a%20tall%20communications%20tower%20with%20glowing%20blue%20fiber%20optic%20connections&width=400&height=300&seq=dept2&orientation=landscape",
                description: "Master the heights of our magical infrastructure, ensuring the uninterrupted flow of arcane data through our network."
              },
              {
                title: "Sales Sorcery",
                icon: "fa-handshake",
                route: "/SalesDept",
                color: "green",
                image: "https://readdy.ai/api/search-image?query=Wizards%20in%20business%20attire%20demonstrating%20magical%20fiber%20optic%20products%20to%20clients&width=400&height=300&seq=dept3&orientation=landscape",
                description: "Learn the mystical arts of persuasion and relationship building to spread our magical connectivity throughout the realm."
              },
              {
                title: "Construction Earth Magic",
                icon: "fa-shovel",
                route: "/ConstructionDept",
                color: "orange",
                image: "https://readdy.ai/api/search-image?query=Wizards%20using%20magical%20tools%20to%20install%20underground%20fiber%20optic%20cables&width=400&height=300&seq=dept4&orientation=landscape",
                description: "Harness the elements to lay the physical foundations of our network, connecting the material world to the digital realm."
              },
              {
                title: "Human Relations",
                icon: "fa-brain",
                route: "/HumanRelationsDept",
                color: "purple",
                image: "https://readdy.ai/api/search-image?query=A%20serene%20office%20space%20with%20floating%20crystal%20orbs%20showing%20employee%20auras&width=400&height=300&seq=dept5&orientation=landscape",
                description: "Harness psychic energies to maintain harmony among our magical workforce, managing personnel records with telepathic precision."
              },
              {
                title: "Network Weavers",
                icon: "fa-diagram-project",
                route: "/NetworkOperations",
                color: "indigo",
                image: "https://readdy.ai/api/search-image?query=Technical%20wizards%20working%20with%20complex%20floating%20network%20diagrams%20made%20of%20blue%20light&width=400&height=300&seq=dept6&orientation=landscape",
                description: "Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom."
              },
              {
                title: "Leadership Council",
                icon: "fa-crown",
                route: "/LeadershipTeam",
                color: "red",
                image: "https://readdy.ai/api/search-image?query=Wise%20magical%20leaders%20in%20an%20elegant%20council%20chamber%20with%20floating%20strategic%20displays&width=400&height=300&seq=dept7&orientation=landscape",
                description: "The guiding council of master technomancers who oversee the strategic direction of our mystical institution."
              }
            ].map((dept, index) => (
              <div key={index} className="bg-white rounded-lg overflow-hidden shadow-lg hover:shadow-xl transition-all duration-300 group">
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={dept.image}
                    alt={dept.title}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-black/70 to-transparent"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-serif font-bold">{dept.title}</h3>
                  </div>
                  <div className="absolute top-4 right-4 w-12 h-12 rounded-full bg-[#15a7dd]/90 flex items-center justify-center shadow-lg">
                    <i className={`fas ${dept.icon} text-white text-lg`}></i>
                  </div>
                </div>
                <div className="p-6">
                  <p className="text-[#475467] mb-6">{dept.description}</p>
                  <Link
                    to={dept.route}
                    className="inline-flex items-center px-6 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300"
                  >
                    <span>Explore Department</span>
                    <i className="fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-1"></i>
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-16 text-center">
            <Link to="/" className="inline-flex items-center px-8 py-3 border-2 border-[#15a7dd] text-[#15a7dd] rounded-full hover:bg-[#15a7dd] hover:text-white transition-all duration-300">
              <i className="fas fa-home mr-2"></i>
              Return to Main Campus
            </Link>
          </div>
        </div>
      </div>
    );
  }

  // Render specific department page
  const renderDepartmentPage = () => {
    switch (currentDept) {
      case 'financial':
        return <FinancialDeptPage />;
      case 'construction':
        return <ConstructionDeptPage />;
      case 'tower':
        return <TowerTechniciansDeptPage />;
      case 'hr':
        return <HumanRelationsDeptPage />;
      case 'sales':
        return <SalesDeptPage />;
      case 'network':
        return <NetworkOperationsDeptPage />;
      case 'leadership':
        return <LeadershipTeamPage />;
      default:
        return (
          <div className="min-h-screen bg-white text-gray-800 font-sans pt-20">
            <div className="container mx-auto px-6 py-12 text-center">
              <h1 className="text-4xl font-serif font-bold mb-6 text-[#15a7dd]">Department Not Found</h1>
              <p className="text-[#475467] mb-8">The requested department could not be found.</p>
              <Link to="/departments" className="inline-flex items-center px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300">
                <i className="fas fa-arrow-left mr-2"></i>
                Back to Departments
              </Link>
            </div>
          </div>
        );
    }
  };

  return renderDepartmentPage();
};

export default DepartmentPages;
