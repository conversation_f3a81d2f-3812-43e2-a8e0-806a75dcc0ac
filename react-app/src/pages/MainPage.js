import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';

const MainPage = ({ user, moodleData }) => {
  const [activeTab, setActiveTab] = useState('home');

  useEffect(() => {
    // Particle animation
    const createParticles = () => {
      const particleContainer = document.getElementById('particle-container');
      if (!particleContainer) return;

      for (let i = 0; i < 30; i++) {
        const particle = document.createElement('div');
        particle.className = 'absolute w-1 h-1 rounded-full bg-blue-500 opacity-0';
        // Random position
        particle.style.left = `${Math.random() * 100}%`;
        particle.style.top = `${Math.random() * 100}%`;
        // Random animation duration
        const duration = 3 + Math.random() * 5;
        particle.style.animation = `float ${duration}s ease-in-out infinite`;
        particle.style.animationDelay = `${Math.random() * 5}s`;
        particleContainer.appendChild(particle);
      }
    };

    createParticles();
  }, []);

  const scrollToSection = (sectionId) => {
    const section = document.getElementById(sectionId);
    if (section) {
      section.scrollIntoView({ behavior: 'smooth' });
      setActiveTab(sectionId);
    }
  };

  return (
    <div className="min-h-screen bg-white text-gray-800 font-sans">
      {/* Particle container for magical effects */}
      <div id="particle-container" className="fixed inset-0 pointer-events-none z-0"></div>

      {/* Hero Section */}
      <section id="home" className="relative min-h-screen flex items-center pt-20">
        <div className="absolute inset-0 z-0 overflow-hidden">
          <img
            src="https://readdy.ai/api/search-image?query=A%20majestic%20magical%20castle%20with%20tall%20spires%20and%20towers%20against%20a%20dramatic%20sunset%20sky.%20The%20central%20tower%20features%20glowing%20blue%20magical%20energy%20flowing%20through%20crystalline%20formations.%20Multiple%20magical%20circular%20platforms%20with%20blue%20energy%20rings%20hover%20around%20the%20castle.%20Stone%20pathways%20lead%20to%20the%20grand%20entrance%2C%20while%20mystical%20blue%20banners%20flutter%20in%20the%20wind.%20Mountains%20and%20waterfalls%20frame%20the%20scene%20with%20ethereal%20lighting&width=1920&height=1080&seq=hero2&orientation=landscape"
            alt="Magical Castle"
            className="w-full h-full object-cover object-center animate-gentle-zoom"
          />
          <div className="absolute inset-0 bg-gradient-to-r from-[#201f1f]/60 via-transparent to-transparent"></div>
          <div className="absolute inset-0">
            <div className="absolute bottom-0 left-0 right-0 h-32 bg-gradient-to-t from-[#201f1f]/40 to-transparent"></div>
            {Array.from({ length: 5 }).map((_, i) => (
              <div
                key={i}
                className="absolute w-32 h-32 rounded-full"
                style={{
                  left: `${Math.random() * 100}%`,
                  top: `${Math.random() * 100}%`,
                  background: 'radial-gradient(circle, rgba(21, 167, 221, 0.2) 0%, rgba(21, 167, 221, 0) 70%)',
                  animation: `pulse ${2 + Math.random() * 2}s infinite`,
                  transform: 'translate(-50%, -50%)'
                }}
              />
            ))}
          </div>
        </div>

        <div className="container mx-auto px-6 flex flex-col md:flex-row items-center relative z-10">
          <div className="w-full md:w-1/2 text-white mb-10 md:mb-0">
            <h1 className="text-4xl md:text-5xl lg:text-6xl font-serif font-bold mb-6">
              <span className="text-[#15a7dd]">Fiber Optic</span> Technomancy
            </h1>
            <p className="text-lg md:text-xl mb-8 text-gray-100">
              Welcome to Fatbeam Fiber University, where the ancient art of magical networking meets modern fiber technology. Begin your journey into the mystical realm of high-speed connectivity.
            </p>
            <div className="flex flex-col sm:flex-row space-y-4 sm:space-y-0 sm:space-x-4">
              <Link
                to="/library"
                className="px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer"
              >
                <i className="fas fa-book-open mr-2"></i>
                Arcane Library
              </Link>
              <Link
                to="/classroom"
                className="px-8 py-3 bg-transparent border-2 border-[#15a7dd] text-white rounded-full hover:bg-[#15a7dd]/20 transition-all duration-300 flex items-center justify-center rounded-button whitespace-nowrap cursor-pointer"
              >
                <i className="fas fa-hat-wizard mr-2"></i>
                Training Hall
              </Link>
            </div>
          </div>

          <div className="absolute top-4 right-4 z-20">
            <button
              onClick={() => {
                const heroImage = document.querySelector('.animate-gentle-zoom');
                if (heroImage) {
                  heroImage.style.animationPlayState = heroImage.style.animationPlayState === 'paused' ? 'running' : 'paused';
                }
              }}
              className="w-12 h-12 rounded-full bg-[#15a7dd]/20 flex items-center justify-center cursor-pointer hover:bg-[#15a7dd]/30 transition-colors duration-300 rounded-button whitespace-nowrap animate-continuous-fade"
            >
              <div className="w-8 h-8 rounded-full bg-[#15a7dd]/30 flex items-center justify-center">
                <i className="fas fa-play text-white text-sm"></i>
              </div>
            </button>
          </div>

          <div className="absolute -bottom-5 -right-5 bg-[#6a3293] text-white p-4 rounded-lg shadow-lg transform rotate-3">
            <p className="text-sm font-medium">
              <i className="fas fa-star mr-1"></i>
              New Courses Added Weekly
            </p>
          </div>
        </div>

        <div className="absolute bottom-10 left-1/2 transform -translate-x-1/2 text-white animate-bounce">
          <button onClick={() => scrollToSection('about')} className="flex flex-col items-center rounded-button whitespace-nowrap cursor-pointer">
            <span className="text-sm mb-2">Scroll to Discover</span>
            <i className="fas fa-chevron-down"></i>
          </button>
        </div>
      </section>

      {/* About Section */}
      <section id="about" className="py-20 bg-[#f2f2f3]">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold mb-4">
              <span className="text-[#15a7dd]">About</span> Our Mystical Academy
            </h2>
            <div className="w-20 h-1 bg-[#15a7dd] mx-auto"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 gap-12 items-center">
            <div>
              <h3 className="text-2xl font-serif font-bold mb-6 text-[#475467]">Where Technology Meets Magic</h3>
              <p className="text-[#475467] mb-6">
                Fatbeam Fiber University was founded in the ancient year of 2010 by a council of network sorcerers who sought to blend the arcane arts with cutting-edge fiber technology. Our institution stands as a beacon of knowledge in the realm of digital enchantment.
              </p>
              <p className="text-[#475467] mb-6">
                Our mystical instructors harness the power of light itself, channeling it through glass threads to connect the farthest reaches of our kingdom. Students learn to weave spells of connectivity, cast protective wards against cyber threats, and summon data from the ethereal cloud.
              </p>

              <div className="flex items-center space-x-4">
                <div className="w-12 h-12 rounded-full bg-[#15a7dd] flex items-center justify-center">
                  <i className="fas fa-graduation-cap text-white"></i>
                </div>
                <div>
                  <h4 className="font-bold text-[#475467]">500+ Graduates</h4>
                  <p className="text-sm text-[#475467]">Master Technomancers in the field</p>
                </div>
              </div>
            </div>

            <div className="relative">
              <img
                src="https://readdy.ai/api/search-image?query=A%20mystical%20library%20with%20floating%20holographic%20screens%20displaying%20network%20diagrams.%20Ancient%20tomes%20with%20glowing%20blue%20runes%20sit%20on%20shelves%20alongside%20modern%20server%20equipment.%20Blue%20magical%20energy%20flows%20between%20books%20and%20digital%20displays.%20Fiber%20optic%20cables%20emit%20blue%20light%20across%20the%20room&width=600&height=600&seq=about1&orientation=squarish"
                alt="Mystical Academy"
                className="w-full h-auto rounded-lg shadow-xl"
              />
              <div className="absolute -bottom-6 -left-6 bg-white p-4 rounded-lg shadow-lg">
                <div className="flex items-center space-x-2">
                  <div className="w-10 h-10 rounded-full bg-[#6a3293] flex items-center justify-center">
                    <i className="fas fa-magic text-white"></i>
                  </div>
                  <div>
                    <h4 className="font-bold text-[#475467]">15 Years</h4>
                    <p className="text-xs text-[#475467]">Of Magical Innovation</p>
                  </div>
                </div>
              </div>
            </div>
          </div>
        </div>
      </section>

      {/* Departments Section */}
      <section id="departments" className="py-20 bg-white">
        <div className="container mx-auto px-6">
          <div className="text-center mb-16">
            <h2 className="text-3xl md:text-4xl font-serif font-bold mb-4">
              <span className="text-[#15a7dd]">Mystical</span> Departments
            </h2>
            <p className="text-[#475467] max-w-2xl mx-auto">
              Explore our specialized schools of technomancy, each focusing on a different aspect of the magical fiber arts.
            </p>
            <div className="w-20 h-1 bg-[#15a7dd] mx-auto mt-4"></div>
          </div>

          <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-8">
            {[
              {
                title: "Finance Alchemy",
                icon: "fa-coins",
                route: "/FinanceDept",
                image: "https://readdy.ai/api/search-image?query=A%20mystical%20treasury%20room%20with%20floating%20gold%20coins%20and%20glowing%20blue%20financial%20charts.%20Ancient%20ledgers%20with%20magical%20runes%20sit%20beside%20modern%20computers.%20Blue%20magical%20energy%20connects%20ledgers%20to%20digital%20displays.%20Wizards%20in%20business%20attire%20cast%20financial%20spells&width=400&height=300&seq=dept1&orientation=landscape",
                description: "Transform raw resources into financial prosperity through the ancient arts of budget management and revenue enchantment."
              },
              {
                title: "Tower Levitation",
                icon: "fa-tower-broadcast",
                route: "/TowerTechnicians",
                image: "https://readdy.ai/api/search-image?query=Magical%20technicians%20working%20on%20a%20tall%20communications%20tower%20with%20glowing%20blue%20fiber%20optic%20connections.%20Wizards%20in%20safety%20gear%20casting%20maintenance%20spells.%20Blue%20energy%20flows%20through%20the%20tower%20structure.%20Technical%20diagrams%20float%20as%20magical%20holograms%20around%20the%20workers&width=400&height=300&seq=dept2&orientation=landscape",
                description: "Master the heights of our magical infrastructure, ensuring the uninterrupted flow of arcane data through our network."
              },
              {
                title: "Sales Sorcery",
                icon: "fa-handshake",
                route: "/SalesDept",
                image: "https://readdy.ai/api/search-image?query=Wizards%20in%20business%20attire%20demonstrating%20magical%20fiber%20optic%20products%20to%20clients.%20Glowing%20blue%20holograms%20show%20network%20capabilities.%20Magical%20contracts%20with%20glowing%20signatures.%20Enchanted%20presentation%20room%20with%20fiber%20optic%20decorations%20and%20blue%20energy%20flowing%20through%20displays&width=400&height=300&seq=dept3&orientation=landscape",
                description: "Learn the mystical arts of persuasion and relationship building to spread our magical connectivity throughout the realm."
              },
              {
                title: "Construction Earth Magic",
                icon: "fa-shovel",
                route: "/ConstructionDept",
                image: "https://readdy.ai/api/search-image?query=Wizards%20using%20magical%20tools%20to%20install%20underground%20fiber%20optic%20cables.%20Earth%20elementals%20helping%20to%20move%20soil.%20Blue%20glowing%20trenches%20with%20fiber%20lines%20being%20laid.%20Magical%20mapping%20tools%20projecting%20underground%20pathways.%20Construction%20site%20with%20both%20magical%20and%20modern%20equipment&width=400&height=300&seq=dept4&orientation=landscape",
                description: "Harness the elements to lay the physical foundations of our network, connecting the material world to the digital realm."
              },
              {
                title: "Human Relations",
                icon: "fa-brain",
                route: "/HumanRelationsDept",
                image: "https://readdy.ai/api/search-image?query=A%20serene%20office%20space%20with%20floating%20crystal%20orbs%20showing%20employee%20auras.%20Psychic%20HR%20wizards%20conducting%20telepathic%20interviews.%20Glowing%20blue%20filing%20cabinets%20with%20magical%20employee%20records.%20Meditation%20areas%20with%20floating%20comfort%20crystals.%20Ethereal%20blue%20energy%20flows%20connecting%20minds&width=400&height=300&seq=dept5&orientation=landscape",
                description: "Harness psychic energies to maintain harmony among our magical workforce, managing personnel records with telepathic precision and empathic understanding."
              },
              {
                title: "Network Weavers",
                icon: "fa-diagram-project",
                route: "/NetworkOperations",
                image: "https://readdy.ai/api/search-image?query=Technical%20wizards%20working%20with%20complex%20floating%20network%20diagrams%20made%20of%20blue%20light.%20Magical%20server%20rooms%20with%20glowing%20fiber%20connections.%20Engineers%20casting%20spells%20to%20optimize%20data%20flow.%20Enchanted%20tools%20analyzing%20network%20performance%20with%20magical%20visualizations&width=400&height=300&seq=dept6&orientation=landscape",
                description: "Craft the invisible pathways of data, ensuring seamless communication across the vast distances of our kingdom."
              }
            ].map((dept, index) => (
              <div key={index} className="bg-white rounded-lg overflow-hidden shadow-md hover:shadow-xl transition-all duration-300 group cursor-pointer">
                <div className="relative h-48 overflow-hidden">
                  <img
                    src={dept.image}
                    alt={dept.title}
                    className="w-full h-full object-cover transition-transform duration-500 group-hover:scale-110"
                  />
                  <div className="absolute inset-0 bg-gradient-to-t from-[#201f1f] to-transparent opacity-70 transition-opacity duration-300 group-hover:opacity-50"></div>
                  <div className="absolute bottom-4 left-4 text-white">
                    <h3 className="text-xl font-serif font-bold">{dept.title}</h3>
                  </div>
                  <div className="absolute top-4 right-4 w-10 h-10 rounded-full bg-[#15a7dd]/90 flex items-center justify-center shadow-lg backdrop-blur-sm ring-2 ring-white/30">
                    <i className={`fas ${dept.icon} text-white text-lg drop-shadow-[0_2px_4px_rgba(255,255,255,0.3)]`}></i>
                  </div>
                </div>
                <div className="p-6">
                  <p className="text-[#475467] mb-4">{dept.description}</p>
                  <Link
                    to={dept.route}
                    className="text-[#15a7dd] font-medium flex items-center rounded-button whitespace-nowrap cursor-pointer"
                  >
                    <span>Explore Department</span>
                    <i className="fas fa-arrow-right ml-2 transition-transform duration-300 group-hover:translate-x-2"></i>
                  </Link>
                </div>
              </div>
            ))}
          </div>

          <div className="mt-16 text-center">
            <Link to="/departments" className="px-8 py-3 bg-[#15a7dd] text-white rounded-full hover:bg-[#1397c7] transition-all duration-300 rounded-button whitespace-nowrap cursor-pointer inline-flex items-center">
              <i className="fas fa-book mr-2"></i>
              View All Departments
            </Link>
          </div>
        </div>
      </section>
    </div>
  );
};

export default MainPage;
