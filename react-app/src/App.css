/* Global styles for the Learning Hub */
.App {
  text-align: left;
}

/* Tailwind CSS will be added via CDN in index.html for now */
/* In production, install Tailwind CSS properly */

/* Custom animations */
@keyframes float {
  0%, 100% {
    transform: translateY(0) scale(1);
    opacity: 0.2;
  }
  50% {
    transform: translateY(-20px) scale(1.5);
    opacity: 0.5;
  }
}

@keyframes gentle-zoom {
  0% {
    transform: scale(1);
  }
  50% {
    transform: scale(1.2);
  }
  100% {
    transform: scale(1);
  }
}

@keyframes pulse {
  0% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1);
  }
  50% {
    opacity: 0.6;
    transform: translate(-50%, -50%) scale(1.2);
  }
  100% {
    opacity: 0.4;
    transform: translate(-50%, -50%) scale(1);
  }
}

@keyframes continuous-fade {
  0% {
    opacity: 0.4;
    transform: scale(0.95);
  }
  50% {
    opacity: 1;
    transform: scale(1.05);
  }
  100% {
    opacity: 0.4;
    transform: scale(0.95);
  }
}

.animate-continuous-fade {
  animation: continuous-fade 3s ease-in-out infinite;
}

.animate-gentle-zoom {
  animation: gentle-zoom 20s ease-out forwards;
}

.animate-video-fade {
  animation: video-fade 2s ease-in-out forwards;
}

/* Utility classes */
.rounded-button {
  border-radius: 9999px;
}

.whitespace-nowrap {
  white-space: nowrap;
}

/* Responsive design helpers */
@media (max-width: 768px) {
  .container {
    padding-left: 1rem;
    padding-right: 1rem;
  }
}
