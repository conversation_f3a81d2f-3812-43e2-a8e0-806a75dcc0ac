import React, { useState, useEffect } from 'react';
import { Link, useLocation } from 'react-router-dom';

const Navigation = ({ user }) => {
  const location = useLocation();
  const [isScrolled, setIsScrolled] = useState(false);
  const [isMenuOpen, setIsMenuOpen] = useState(false);
  
  const isActive = (path) => location.pathname.startsWith(path);
  
  useEffect(() => {
    const handleScroll = () => {
      if (window.scrollY > 50) {
        setIsScrolled(true);
      } else {
        setIsScrolled(false);
      }
    };
    
    window.addEventListener('scroll', handleScroll);
    return () => window.removeEventListener('scroll', handleScroll);
  }, []);
  
  return (
    <nav className={`fixed top-0 left-0 right-0 z-50 transition-all duration-300 ${isScrolled ? 'bg-white shadow-md py-2' : 'bg-transparent py-4'}`}>
      <div className="container mx-auto px-6 flex justify-between items-center">
        <Link to="/" className="flex items-center">
          <div className="relative w-16 h-16 overflow-hidden">
            <img
              src="https://www.fatbeamfiber.com/hubfs/site-files/logo-fatbeam-fiber.svg"
              alt="Fatbeam Fiber"
              className="w-full h-full object-contain brightness-200"
            />
            <div className="absolute inset-0 bg-[#15a7dd] opacity-20 animate-pulse rounded-full"></div>
          </div>
          <h1 className={`ml-3 text-2xl font-serif font-bold transition-colors duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)] ${isScrolled ? 'text-[#15a7dd]' : 'text-white'}`}>
            <span className={`transition-colors duration-300 ${isScrolled ? 'text-[#15a7dd]' : 'text-white'}`}>Fatbeam</span> Fiber University
          </h1>
        </Link>
        
        <div className="hidden md:flex space-x-8">
          <Link 
            to="/" 
            className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]
            ${isScrolled
              ? (isActive('/') && location.pathname === '/' ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-[#15a7dd] hover:text-[#1397c7]')
              : (isActive('/') && location.pathname === '/' ? 'text-white border-b-2 border-white' : 'text-white hover:text-gray-200')
            }`}
          >
            Home
          </Link>
          <Link 
            to="/departments" 
            className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]
            ${isScrolled
              ? (isActive('/departments') ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-[#15a7dd] hover:text-[#1397c7]')
              : (isActive('/departments') ? 'text-white border-b-2 border-white' : 'text-white hover:text-gray-200')
            }`}
          >
            Departments
          </Link>
          <Link 
            to="/library" 
            className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]
            ${isScrolled
              ? (isActive('/library') ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-[#15a7dd] hover:text-[#1397c7]')
              : (isActive('/library') ? 'text-white border-b-2 border-white' : 'text-white hover:text-gray-200')
            }`}
          >
            Library
          </Link>
          <a 
            href="/learning" 
            className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]
            ${isScrolled ? 'text-[#15a7dd] hover:text-[#1397c7]' : 'text-white hover:text-gray-200'}`}
          >
            Learning
          </a>
          <Link 
            to="/classroom" 
            className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]
            ${isScrolled
              ? (isActive('/classroom') ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-[#15a7dd] hover:text-[#1397c7]')
              : (isActive('/classroom') ? 'text-white border-b-2 border-white' : 'text-white hover:text-gray-200')
            }`}
          >
            Classroom
          </Link>
          {user && (
            <Link 
              to="/dashboard" 
              className={`text-lg font-medium rounded-button whitespace-nowrap cursor-pointer transition-all duration-300 drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]
              ${isScrolled
                ? (isActive('/dashboard') ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]' : 'text-[#15a7dd] hover:text-[#1397c7]')
                : (isActive('/dashboard') ? 'text-white border-b-2 border-white' : 'text-white hover:text-gray-200')
              }`}
            >
              Dashboard
            </Link>
          )}
        </div>
        
        <div>
          {user ? (
            <span className={`transition-colors duration-300 ${isScrolled ? 'text-gray-600' : 'text-white'}`}>
              Welcome, {user.firstname}
            </span>
          ) : (
            <a href="/learning" className={`transition-colors duration-300 ${isScrolled ? 'text-blue-600 hover:text-blue-800' : 'text-white hover:text-gray-200'}`}>
              Login
            </a>
          )}
        </div>
        
        {/* Mobile Navigation Toggle */}
        <div className="md:hidden">
          <button
            onClick={() => setIsMenuOpen(!isMenuOpen)}
            className={`focus:outline-none rounded-button whitespace-nowrap cursor-pointer transition-colors duration-300 ${isScrolled ? 'text-[#15a7dd]' : 'text-white'} drop-shadow-[0_2px_2px_rgba(0,0,0,0.3)]`}
          >
            <i className={`fas ${isMenuOpen ? 'fa-times' : 'fa-bars'} text-2xl`}></i>
          </button>
        </div>
      </div>
      
      {/* Mobile Menu */}
      {isMenuOpen && (
        <div className="md:hidden bg-white shadow-lg absolute top-full left-0 right-0 py-4">
          <div className="container mx-auto px-6 flex flex-col space-y-4">
            <Link to="/" className="text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]">
              Home
            </Link>
            <Link to="/departments" className="text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]">
              Departments
            </Link>
            <Link to="/library" className="text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]">
              Library
            </Link>
            <a href="/learning" className="text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]">
              Learning
            </a>
            <Link to="/classroom" className="text-base font-medium py-2 rounded-button whitespace-nowrap cursor-pointer text-[#475467]">
              Classroom
            </Link>
          </div>
        </div>
      )}
    </nav>
  );
};

export default Navigation;
