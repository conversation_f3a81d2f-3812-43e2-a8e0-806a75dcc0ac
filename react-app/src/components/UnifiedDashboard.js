import React, { useState, useEffect } from 'react';
import { Link } from 'react-router-dom';
import MoodleIntegration from './MoodleIntegration';

const UnifiedDashboard = ({ user }) => {
  const [moodleData, setMoodleData] = useState(null);
  const [loading, setLoading] = useState(true);
  const [activeSection, setActiveSection] = useState('overview');

  useEffect(() => {
    // Fetch Moodle data when component mounts
    const fetchMoodleData = async () => {
      try {
        setLoading(true);
        // This will be implemented with actual Moodle API calls
        // For now, using mock data
        const mockData = {
          courses: [
            { id: 1, name: 'Fundamentals of Magical Networking', progress: 85 },
            { id: 2, name: 'Fiber Optic Enchantments', progress: 60 },
            { id: 3, name: 'Network Security Wards', progress: 30 }
          ],
          recentActivity: [
            { type: 'course_completion', course: 'Basic Network Spells', timestamp: '2024-01-15' },
            { type: 'assignment_submission', course: 'Fiber Optics 101', timestamp: '2024-01-14' }
          ],
          notifications: [
            { type: 'assignment_due', message: 'Assignment due in 3 days', course: 'Network Security' },
            { type: 'new_course', message: 'New course available: Advanced Routing', course: null }
          ]
        };
        setMoodleData(mockData);
      } catch (error) {
        console.error('Error fetching Moodle data:', error);
      } finally {
        setLoading(false);
      }
    };

    fetchMoodleData();
  }, []);

  if (loading) {
    return (
      <div className="min-h-screen bg-gradient-to-br from-[#0a0500] via-[#1a0f00] to-[#2a1a05] text-white pt-20">
        <div className="container mx-auto px-6 py-12">
          <div className="flex items-center justify-center h-64">
            <div className="text-center">
              <i className="fas fa-spinner fa-spin text-4xl text-[#15a7dd] mb-4"></i>
              <p className="text-xl">Loading your magical dashboard...</p>
            </div>
          </div>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gradient-to-br from-[#0a0500] via-[#1a0f00] to-[#2a1a05] text-white pt-20">
      <div className="container mx-auto px-6 py-12">
        {/* Header */}
        <div className="mb-12">
          <h1 className="text-4xl md:text-5xl font-serif font-bold mb-4">
            <span className="text-[#15a7dd]">Unified</span> Learning Dashboard
          </h1>
          <p className="text-xl text-gray-300 max-w-3xl">
            Your central command center for all learning activities across the React frontend and Moodle backend.
          </p>
        </div>

        {/* Navigation Tabs */}
        <div className="flex flex-wrap border-b border-gray-700 mb-8">
          {['overview', 'courses', 'progress', 'integration'].map((section) => (
            <button
              key={section}
              onClick={() => setActiveSection(section)}
              className={`px-6 py-3 font-medium text-sm transition-colors duration-300 rounded-button whitespace-nowrap cursor-pointer ${
                activeSection === section
                  ? 'text-[#15a7dd] border-b-2 border-[#15a7dd]'
                  : 'text-gray-400 hover:text-[#15a7dd]'
              }`}
            >
              {section.charAt(0).toUpperCase() + section.slice(1)}
            </button>
          ))}
        </div>

        {/* Overview Section */}
        {activeSection === 'overview' && (
          <div className="grid grid-cols-1 lg:grid-cols-3 gap-8">
            <div className="lg:col-span-2">
              <h2 className="text-2xl font-serif font-bold mb-6">Learning Overview</h2>
              
              {/* Quick Stats */}
              <div className="grid grid-cols-1 md:grid-cols-3 gap-6 mb-8">
                <div className="bg-gradient-to-br from-[#15a7dd]/20 to-[#15a7dd]/10 rounded-lg p-6 border border-[#15a7dd]/30">
                  <h3 className="text-lg font-semibold mb-2">Active Courses</h3>
                  <div className="text-3xl font-bold text-[#15a7dd]">{moodleData?.courses?.length || 0}</div>
                </div>
                
                <div className="bg-gradient-to-br from-[#6a3293]/20 to-[#6a3293]/10 rounded-lg p-6 border border-[#6a3293]/30">
                  <h3 className="text-lg font-semibold mb-2">Avg Progress</h3>
                  <div className="text-3xl font-bold text-[#6a3293]">
                    {moodleData?.courses ? Math.round(moodleData.courses.reduce((acc, course) => acc + course.progress, 0) / moodleData.courses.length) : 0}%
                  </div>
                </div>
                
                <div className="bg-gradient-to-br from-yellow-500/20 to-yellow-500/10 rounded-lg p-6 border border-yellow-500/30">
                  <h3 className="text-lg font-semibold mb-2">Notifications</h3>
                  <div className="text-3xl font-bold text-yellow-500">{moodleData?.notifications?.length || 0}</div>
                </div>
              </div>

              {/* Recent Activity */}
              <div className="bg-[#1a0f00] rounded-lg p-6 border border-gray-700">
                <h3 className="text-xl font-semibold mb-4">Recent Activity</h3>
                <div className="space-y-4">
                  {moodleData?.recentActivity?.map((activity, index) => (
                    <div key={index} className="flex items-center justify-between p-3 bg-[#2a1a05] rounded">
                      <div className="flex items-center">
                        <i className={`fas ${activity.type === 'course_completion' ? 'fa-check-circle text-green-500' : 'fa-upload text-blue-500'} mr-3`}></i>
                        <div>
                          <p className="font-medium">{activity.type.replace('_', ' ').toUpperCase()}</p>
                          <p className="text-sm text-gray-400">{activity.course}</p>
                        </div>
                      </div>
                      <span className="text-sm text-gray-400">{activity.timestamp}</span>
                    </div>
                  )) || (
                    <p className="text-gray-400 text-center py-4">No recent activity</p>
                  )}
                </div>
              </div>
            </div>

            <div>
              <h2 className="text-2xl font-serif font-bold mb-6">Quick Actions</h2>
              <div className="space-y-4">
                <Link to="/classroom" className="block w-full p-4 bg-[#15a7dd] rounded-lg hover:bg-[#1397c7] transition-colors duration-300">
                  <div className="flex items-center">
                    <i className="fas fa-chalkboard-teacher mr-3 text-xl"></i>
                    <div>
                      <h3 className="font-semibold">My Classroom</h3>
                      <p className="text-sm opacity-90">Personal learning space</p>
                    </div>
                  </div>
                </Link>

                <a href="/learning" className="block w-full p-4 bg-[#6a3293] rounded-lg hover:bg-[#5a2283] transition-colors duration-300">
                  <div className="flex items-center">
                    <i className="fas fa-graduation-cap mr-3 text-xl"></i>
                    <div>
                      <h3 className="font-semibold">Moodle LMS</h3>
                      <p className="text-sm opacity-90">Access full course system</p>
                    </div>
                  </div>
                </a>

                <Link to="/library" className="block w-full p-4 bg-yellow-600 rounded-lg hover:bg-yellow-700 transition-colors duration-300">
                  <div className="flex items-center">
                    <i className="fas fa-book mr-3 text-xl"></i>
                    <div>
                      <h3 className="font-semibold">Library</h3>
                      <p className="text-sm opacity-90">Browse resources</p>
                    </div>
                  </div>
                </Link>

                <Link to="/departments" className="block w-full p-4 bg-green-600 rounded-lg hover:bg-green-700 transition-colors duration-300">
                  <div className="flex items-center">
                    <i className="fas fa-building mr-3 text-xl"></i>
                    <div>
                      <h3 className="font-semibold">Departments</h3>
                      <p className="text-sm opacity-90">Explore specializations</p>
                    </div>
                  </div>
                </Link>
              </div>

              {/* Notifications */}
              <div className="mt-8 bg-[#1a0f00] rounded-lg p-6 border border-gray-700">
                <h3 className="text-xl font-semibold mb-4">Notifications</h3>
                <div className="space-y-3">
                  {moodleData?.notifications?.map((notification, index) => (
                    <div key={index} className="p-3 bg-[#2a1a05] rounded border-l-4 border-yellow-500">
                      <p className="text-sm font-medium">{notification.message}</p>
                      {notification.course && (
                        <p className="text-xs text-gray-400 mt-1">{notification.course}</p>
                      )}
                    </div>
                  )) || (
                    <p className="text-gray-400 text-center py-4">No notifications</p>
                  )}
                </div>
              </div>
            </div>
          </div>
        )}

        {/* Courses Section */}
        {activeSection === 'courses' && (
          <div>
            <h2 className="text-2xl font-serif font-bold mb-6">Course Management</h2>
            <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
              {moodleData?.courses?.map((course) => (
                <div key={course.id} className="bg-[#1a0f00] rounded-lg p-6 border border-gray-700">
                  <h3 className="text-xl font-semibold mb-4">{course.name}</h3>
                  <div className="mb-4">
                    <div className="flex justify-between text-sm mb-2">
                      <span>Progress</span>
                      <span>{course.progress}%</span>
                    </div>
                    <div className="w-full bg-gray-700 rounded-full h-2">
                      <div className="bg-[#15a7dd] h-2 rounded-full" style={{ width: `${course.progress}%` }}></div>
                    </div>
                  </div>
                  <a href="/learning" className="block w-full text-center py-2 bg-[#15a7dd] rounded hover:bg-[#1397c7] transition-colors duration-300">
                    Open in Moodle
                  </a>
                </div>
              )) || (
                <div className="col-span-full text-center py-12">
                  <p className="text-gray-400">No courses found</p>
                </div>
              )}
            </div>
          </div>
        )}

        {/* Progress Section */}
        {activeSection === 'progress' && (
          <div>
            <h2 className="text-2xl font-serif font-bold mb-6">Learning Progress</h2>
            <div className="bg-[#1a0f00] rounded-lg p-6 border border-gray-700">
              <p className="text-center text-gray-400 py-12">
                <i className="fas fa-chart-line text-4xl mb-4 block"></i>
                Detailed progress analytics coming soon!
              </p>
            </div>
          </div>
        )}

        {/* Integration Section */}
        {activeSection === 'integration' && (
          <div>
            <h2 className="text-2xl font-serif font-bold mb-6">React-Moodle Integration</h2>
            <MoodleIntegration />
          </div>
        )}
      </div>
    </div>
  );
};

export default UnifiedDashboard;
