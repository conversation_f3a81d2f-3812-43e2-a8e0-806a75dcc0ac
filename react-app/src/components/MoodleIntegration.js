import React, { useState, useEffect } from 'react';
import moodle<PERSON><PERSON> from '../services/moodleAPI';

const MoodleIntegration = () => {
  const [connectionStatus, setConnectionStatus] = useState('checking');
  const [apiData, setApiData] = useState(null);
  const [error, setError] = useState(null);

  useEffect(() => {
    checkMoodleConnection();
  }, []);

  const checkMoodleConnection = async () => {
    try {
      setConnectionStatus('checking');
      const siteInfo = await moodleAPI.getSiteInfo();
      setApiData(siteInfo);
      setConnectionStatus('connected');
      setError(null);
    } catch (err) {
      setConnectionStatus('error');
      setError(err.message);
    }
  };

  const testApiCall = async (endpoint) => {
    try {
      let result;
      switch (endpoint) {
        case 'courses':
          result = await moodleAPI.getCourses();
          break;
        case 'users':
          result = await moodleAPI.getCurrentUser();
          break;
        case 'site':
          result = await moodleAPI.getSiteInfo();
          break;
        default:
          result = { error: 'Unknown endpoint' };
      }
      alert(`API Test Result:\n${JSON.stringify(result, null, 2)}`);
    } catch (err) {
      alert(`API Test Error:\n${err.message}`);
    }
  };

  return (
    <div className="space-y-6">
      {/* Connection Status */}
      <div className="bg-[#1a0f00] rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-semibold mb-4">Moodle Connection Status</h3>
        <div className="flex items-center justify-between">
          <div className="flex items-center">
            <div className={`w-4 h-4 rounded-full mr-3 ${
              connectionStatus === 'connected' ? 'bg-green-500' :
              connectionStatus === 'error' ? 'bg-red-500' : 'bg-yellow-500'
            }`}></div>
            <span className="font-medium">
              {connectionStatus === 'connected' ? 'Connected' :
               connectionStatus === 'error' ? 'Connection Error' : 'Checking...'}
            </span>
          </div>
          <button
            onClick={checkMoodleConnection}
            className="px-4 py-2 bg-[#15a7dd] rounded hover:bg-[#1397c7] transition-colors duration-300"
          >
            <i className="fas fa-sync-alt mr-2"></i>
            Refresh
          </button>
        </div>
        
        {error && (
          <div className="mt-4 p-3 bg-red-900/20 border border-red-500/30 rounded">
            <p className="text-red-400 text-sm">{error}</p>
          </div>
        )}
      </div>

      {/* API Configuration */}
      <div className="bg-[#1a0f00] rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-semibold mb-4">API Configuration</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-2">Moodle URL</label>
            <input
              type="text"
              value={process.env.REACT_APP_MOODLE_URL || ''}
              readOnly
              className="w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">API Endpoint</label>
            <input
              type="text"
              value={process.env.REACT_APP_MOODLE_API_URL || ''}
              readOnly
              className="w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Token Status</label>
            <input
              type="text"
              value={process.env.REACT_APP_MOODLE_TOKEN ? 'Configured' : 'Not Set'}
              readOnly
              className="w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300"
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">Connection Type</label>
            <input
              type="text"
              value="REST API"
              readOnly
              className="w-full px-3 py-2 bg-[#2a1a05] border border-gray-600 rounded text-gray-300"
            />
          </div>
        </div>
      </div>

      {/* Site Information */}
      {apiData && (
        <div className="bg-[#1a0f00] rounded-lg p-6 border border-gray-700">
          <h3 className="text-xl font-semibold mb-4">Moodle Site Information</h3>
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <div>
              <label className="block text-sm font-medium mb-2">Site Name</label>
              <p className="text-gray-300">{apiData.sitename || 'N/A'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Version</label>
              <p className="text-gray-300">{apiData.release || 'N/A'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">Language</label>
              <p className="text-gray-300">{apiData.lang || 'N/A'}</p>
            </div>
            <div>
              <label className="block text-sm font-medium mb-2">User ID</label>
              <p className="text-gray-300">{apiData.userid || 'N/A'}</p>
            </div>
          </div>
        </div>
      )}

      {/* API Testing */}
      <div className="bg-[#1a0f00] rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-semibold mb-4">API Testing</h3>
        <p className="text-gray-400 mb-4">Test various Moodle API endpoints to ensure proper integration.</p>
        <div className="grid grid-cols-1 md:grid-cols-3 gap-4">
          <button
            onClick={() => testApiCall('site')}
            className="p-3 bg-blue-600 rounded hover:bg-blue-700 transition-colors duration-300"
          >
            <i className="fas fa-info-circle mr-2"></i>
            Test Site Info
          </button>
          <button
            onClick={() => testApiCall('courses')}
            className="p-3 bg-green-600 rounded hover:bg-green-700 transition-colors duration-300"
          >
            <i className="fas fa-book mr-2"></i>
            Test Courses
          </button>
          <button
            onClick={() => testApiCall('users')}
            className="p-3 bg-purple-600 rounded hover:bg-purple-700 transition-colors duration-300"
          >
            <i className="fas fa-user mr-2"></i>
            Test User Info
          </button>
        </div>
      </div>

      {/* Integration Features */}
      <div className="bg-[#1a0f00] rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-semibold mb-4">Integration Features</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
          <div>
            <h4 className="font-semibold mb-2 text-[#15a7dd]">Implemented Features</h4>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <i className="fas fa-check text-green-500 mr-2"></i>
                Single Sign-On (SSO) Integration
              </li>
              <li className="flex items-center">
                <i className="fas fa-check text-green-500 mr-2"></i>
                Course Data Synchronization
              </li>
              <li className="flex items-center">
                <i className="fas fa-check text-green-500 mr-2"></i>
                User Progress Tracking
              </li>
              <li className="flex items-center">
                <i className="fas fa-check text-green-500 mr-2"></i>
                Unified Navigation
              </li>
            </ul>
          </div>
          <div>
            <h4 className="font-semibold mb-2 text-yellow-500">Planned Features</h4>
            <ul className="space-y-2 text-sm">
              <li className="flex items-center">
                <i className="fas fa-clock text-yellow-500 mr-2"></i>
                Real-time Notifications
              </li>
              <li className="flex items-center">
                <i className="fas fa-clock text-yellow-500 mr-2"></i>
                Grade Synchronization
              </li>
              <li className="flex items-center">
                <i className="fas fa-clock text-yellow-500 mr-2"></i>
                Assignment Submission
              </li>
              <li className="flex items-center">
                <i className="fas fa-clock text-yellow-500 mr-2"></i>
                Discussion Forum Integration
              </li>
            </ul>
          </div>
        </div>
      </div>

      {/* Documentation Links */}
      <div className="bg-[#1a0f00] rounded-lg p-6 border border-gray-700">
        <h3 className="text-xl font-semibold mb-4">Documentation & Resources</h3>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <a
            href="https://docs.moodle.org/dev/Web_service_API_functions"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center p-3 bg-[#2a1a05] rounded hover:bg-[#3a2a15] transition-colors duration-300"
          >
            <i className="fas fa-external-link-alt mr-3 text-[#15a7dd]"></i>
            <div>
              <h4 className="font-semibold">Moodle Web Services API</h4>
              <p className="text-sm text-gray-400">Official API documentation</p>
            </div>
          </a>
          <a
            href="https://docs.moodle.org/dev/Creating_a_web_service_client"
            target="_blank"
            rel="noopener noreferrer"
            className="flex items-center p-3 bg-[#2a1a05] rounded hover:bg-[#3a2a15] transition-colors duration-300"
          >
            <i className="fas fa-external-link-alt mr-3 text-[#15a7dd]"></i>
            <div>
              <h4 className="font-semibold">Web Service Client Guide</h4>
              <p className="text-sm text-gray-400">Integration best practices</p>
            </div>
          </a>
        </div>
      </div>
    </div>
  );
};

export default MoodleIntegration;
