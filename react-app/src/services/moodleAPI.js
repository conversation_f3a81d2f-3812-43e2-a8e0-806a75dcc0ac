import axios from 'axios';

class MoodleAPI {
  constructor() {
    this.baseURL = process.env.REACT_APP_MOODLE_API_URL;
    this.token = process.env.REACT_APP_MOODLE_TOKEN;
    this.moodleURL = process.env.REACT_APP_MOODLE_URL;
    
    // Create axios instance with default config
    this.api = axios.create({
      baseURL: this.baseURL,
      timeout: 10000,
      headers: {
        'Content-Type': 'application/x-www-form-urlencoded',
      },
    });
  }

  // Helper method to make API calls
  async makeRequest(wsfunction, params = {}) {
    try {
      const data = new URLSearchParams({
        wstoken: this.token,
        wsfunction: wsfunction,
        moodlewsrestformat: 'json',
        ...params
      });

      const response = await this.api.post('', data);
      
      if (response.data.exception) {
        throw new Error(response.data.message || 'Moodle API Error');
      }
      
      return response.data;
    } catch (error) {
      if (error.response) {
        throw new Error(`API Error: ${error.response.status} - ${error.response.statusText}`);
      } else if (error.request) {
        throw new Error('Network Error: Unable to connect to Moodle');
      } else {
        throw new Error(error.message);
      }
    }
  }

  // Get site information
  async getSiteInfo() {
    return await this.makeRequest('core_webservice_get_site_info');
  }

  // Get current user information
  async getCurrentUser() {
    const siteInfo = await this.getSiteInfo();
    return {
      id: siteInfo.userid,
      username: siteInfo.username,
      firstname: siteInfo.firstname,
      lastname: siteInfo.lastname,
      email: siteInfo.useremail,
      lang: siteInfo.lang
    };
  }

  // Get user's courses
  async getCourses(userid = null) {
    const params = userid ? { userid } : {};
    return await this.makeRequest('core_enrol_get_users_courses', params);
  }

  // Get course contents
  async getCourseContents(courseid) {
    return await this.makeRequest('core_course_get_contents', { courseid });
  }

  // Get user's grades for a course
  async getCourseGrades(courseid, userid = null) {
    const params = { courseid };
    if (userid) params.userid = userid;
    return await this.makeRequest('gradereport_user_get_grade_items', params);
  }

  // Get user's assignments
  async getAssignments(courseids = []) {
    const params = courseids.length > 0 ? { courseids } : {};
    return await this.makeRequest('mod_assign_get_assignments', params);
  }

  // Get user's calendar events
  async getCalendarEvents(options = {}) {
    const defaultOptions = {
      events: {
        eventids: [],
        courseids: [],
        groupids: [],
        categoryids: []
      },
      options: {
        userevents: true,
        siteevents: true,
        timestart: Math.floor(Date.now() / 1000),
        timeend: Math.floor(Date.now() / 1000) + (30 * 24 * 60 * 60) // 30 days
      }
    };
    
    const mergedOptions = { ...defaultOptions, ...options };
    return await this.makeRequest('core_calendar_get_calendar_events', mergedOptions);
  }

  // Get user's notifications
  async getNotifications(useridto = null, limit = 20) {
    const params = { limit };
    if (useridto) params.useridto = useridto;
    return await this.makeRequest('message_popup_get_popup_notifications', params);
  }

  // Get course participants
  async getCourseParticipants(courseid) {
    return await this.makeRequest('core_enrol_get_enrolled_users', { courseid });
  }

  // Get user's recent activity
  async getRecentActivity(courseid, since = null) {
    const params = { courseid };
    if (since) params.since = since;
    return await this.makeRequest('core_course_get_recent_courses', params);
  }

  // Search courses
  async searchCourses(criterianame, criteriavalue, page = 0, perpage = 20) {
    return await this.makeRequest('core_course_search_courses', {
      criterianame,
      criteriavalue,
      page,
      perpage
    });
  }

  // Get user preferences
  async getUserPreferences(name = null) {
    const params = name ? { name } : {};
    return await this.makeRequest('core_user_get_user_preferences', params);
  }

  // Set user preferences
  async setUserPreferences(preferences) {
    return await this.makeRequest('core_user_set_user_preferences', { preferences });
  }

  // Get course categories
  async getCourseCategories(criteria = []) {
    return await this.makeRequest('core_course_get_categories', { criteria });
  }

  // Get user's dashboard blocks
  async getDashboardBlocks() {
    return await this.makeRequest('core_block_get_dashboard_blocks');
  }

  // Get user's completion status for courses
  async getCourseCompletionStatus(courseid, userid = null) {
    const params = { courseid };
    if (userid) params.userid = userid;
    return await this.makeRequest('core_completion_get_course_completion_status', params);
  }

  // Helper method to construct Moodle URLs
  getMoodleURL(path = '') {
    return `${this.moodleURL}${path}`;
  }

  // Helper method to get course URL
  getCourseURL(courseid) {
    return this.getMoodleURL(`/course/view.php?id=${courseid}`);
  }

  // Helper method to get user profile URL
  getUserProfileURL(userid) {
    return this.getMoodleURL(`/user/profile.php?id=${userid}`);
  }

  // Helper method to check if API is configured
  isConfigured() {
    return !!(this.baseURL && this.token && this.moodleURL);
  }

  // Helper method to test connection
  async testConnection() {
    try {
      await this.getSiteInfo();
      return { success: true, message: 'Connection successful' };
    } catch (error) {
      return { success: false, message: error.message };
    }
  }

  // Get user's progress data for dashboard
  async getUserProgress(userid = null) {
    try {
      const [courses, siteInfo] = await Promise.all([
        this.getCourses(userid),
        this.getSiteInfo()
      ]);

      const progressData = {
        totalCourses: courses.length,
        completedCourses: 0,
        inProgressCourses: 0,
        overallProgress: 0,
        courses: []
      };

      // Calculate progress for each course
      for (const course of courses) {
        try {
          const completion = await this.getCourseCompletionStatus(course.id, userid);
          const progress = completion.completionpercentage || 0;
          
          progressData.courses.push({
            ...course,
            progress,
            isCompleted: progress === 100
          });

          if (progress === 100) {
            progressData.completedCourses++;
          } else if (progress > 0) {
            progressData.inProgressCourses++;
          }
        } catch (error) {
          // If completion data is not available, add course with 0 progress
          progressData.courses.push({
            ...course,
            progress: 0,
            isCompleted: false
          });
        }
      }

      // Calculate overall progress
      if (progressData.courses.length > 0) {
        const totalProgress = progressData.courses.reduce((sum, course) => sum + course.progress, 0);
        progressData.overallProgress = Math.round(totalProgress / progressData.courses.length);
      }

      return progressData;
    } catch (error) {
      throw new Error(`Failed to get user progress: ${error.message}`);
    }
  }
}

// Create and export a singleton instance
const moodleAPI = new MoodleAPI();
export default moodleAPI;
