import React from 'react';
import { BrowserRouter as Router, Routes, Route } from 'react-router-dom';
import Navigation from './components/Navigation';
import MainPage from './pages/MainPage';
import DepartmentPages from './pages/DepartmentPages';
import LibraryPages from './pages/LibraryPages';
import ClassroomPage from './pages/ClassroomPage';
import UnifiedDashboard from './components/UnifiedDashboard';
import './App.css';

function App() {
  return (
    <Router>
      <div className="App">
        <Navigation />
        <Routes>
          <Route path="/" element={<MainPage />} />
          <Route path="/departments" element={<DepartmentPages />} />
          <Route path="/departments/:deptName" element={<DepartmentPages />} />
          <Route path="/library" element={<LibraryPages />} />
          <Route path="/classroom" element={<ClassroomPage />} />
          <Route path="/dashboard" element={<UnifiedDashboard />} />
          {/* Department-specific routes */}
          <Route path="/FinanceDept" element={<DepartmentPages deptType="financial" />} />
          <Route path="/ConstructionDept" element={<DepartmentPages deptType="construction" />} />
          <Route path="/TowerTechnicians" element={<DepartmentPages deptType="tower" />} />
          <Route path="/HumanRelationsDept" element={<DepartmentPages deptType="hr" />} />
          <Route path="/SalesDept" element={<DepartmentPages deptType="sales" />} />
          <Route path="/NetworkOperations" element={<DepartmentPages deptType="network" />} />
          <Route path="/LeadershipTeam" element={<DepartmentPages deptType="leadership" />} />
        </Routes>
      </div>
    </Router>
  );
}

export default App;
