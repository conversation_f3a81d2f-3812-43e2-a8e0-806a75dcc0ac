# Production Dockerfile for React Frontend
FROM node:18-alpine as build

# Set working directory
WORKDIR /app

# Install system dependencies
RUN apk add --no-cache git

# Install dependencies
COPY package*.json ./
RUN npm ci --only=production

# Copy configuration files
COPY tailwind.config.js ./
COPY postcss.config.js ./

# Copy source code
COPY . .

# Build the application
RUN npm run build

# Production stage
FROM nginx:alpine

# Copy built application
COPY --from=build /app/build /usr/share/nginx/html

# Copy nginx configuration
COPY nginx.conf /etc/nginx/conf.d/default.conf

# Expose port
EXPOSE 80

# Start nginx
CMD ["nginx", "-g", "daemon off;"]
