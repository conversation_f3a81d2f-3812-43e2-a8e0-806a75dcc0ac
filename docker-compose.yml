# Hybrid React + Moodle Learning Hub
# This configuration supports both React frontend and Moodle backend
version: '3.8'

services:
  # React Frontend Development Server
  react-frontend:
    build:
      context: ./react-app
      dockerfile: Dockerfile.dev
    container_name: learning-hub-react
    environment:
      - CHOKIDAR_USEPOLLING=true
      - REACT_APP_MOODLE_API_URL=http://localhost:3000/learning/webservice/rest/server.php
      - REACT_APP_MOODLE_URL=http://localhost:3000/learning
      - REACT_APP_MOODLE_TOKEN=${MOODLE_API_TOKEN:-your_moodle_token_here}
    volumes:
      - ./react-app:/app
      - /app/node_modules
    networks:
      - learning-hub-net
    depends_on:
      - moodle-php
    restart: unless-stopped

  # Moodle PHP-FPM Service
  moodle-php:
    build: ./moodle-docker/php
    env_file:
      - .env
    environment:
      - MOODLE_DB_HOST=${MOODLE_DB_HOST}
      - MOODLE_DB_NAME=${MOODLE_DB_NAME}
      - MOODLE_DB_USER=${MO<PERSON>LE_DB_USER}
      - MOODLE_DB_PASS=${MOODLE_DB_PASS}
      - MOODLE_DB_PREFIX=${MOODLE_DB_PREFIX}
      - MOODLE_DB_PORT=${MOODLE_DB_PORT}
      - MOODLE_WWWROOT=${MOODLE_WWWROOT}
      - MOODLE_DATAROOT=${MOODLE_DATAROOT}
      - MOODLE_ADMIN=${MOODLE_ADMIN}
      - MOODLE_SESSION_TIMEOUT=${MOODLE_SESSION_TIMEOUT}
      - MOODLE_SESSION_WARNING=${MOODLE_SESSION_WARNING}
      - MOODLE_SESSION_COOKIE=${MOODLE_SESSION_COOKIE}
      - MOODLE_REDIS_HOST=${MOODLE_REDIS_HOST}
      - MOODLE_REDIS_PORT=${MOODLE_REDIS_PORT}
    container_name: learning-hub-moodle
    healthcheck:
      test: ["CMD-SHELL", "php /usr/local/bin/check-extensions.php || exit 1"]
      interval: 60s
      timeout: 30s
      retries: 3
      start_period: 60s
    restart: unless-stopped
    volumes:
      - ./moodle-docker/php/custom-configs/php.ini:/usr/local/etc/php/conf.d/php.ini
      - ./moodle-docker/php/custom-configs/php-fpm.conf:/usr/local/etc/php-fpm.d/php-fpm.conf
      - ./moodle-docker/php/moodledata:/var/moodledata
      - ./moodle-docker/moodle:/var/www/html
      - ./moodle-custom-theme:/var/www/html/theme/learning_hub
      - ./logs/php:/var/log/php
    networks:
      - learning-hub-net
    depends_on:
      - mysql
      - redis

  # Nginx Reverse Proxy
  nginx:
    image: nginx:latest
    container_name: learning-hub-nginx
    healthcheck:
      test: ["CMD-SHELL", "curl -f http://localhost/nginx-health || exit 1"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    ports:
      - "3000:80"
      - "3443:443"
    volumes:
      - ./nginx/nginx.conf:/etc/nginx/nginx.conf:ro
      - ./nginx/conf.d:/etc/nginx/conf.d:ro
      - ./nginx/certs:/etc/nginx/certs:ro
      - ./moodle-docker/moodle:/var/www/html:ro
      - ./moodle-docker/php/moodledata:/var/moodledata:ro
      - ./logs/nginx:/var/log/nginx
    depends_on:
      - react-frontend
      - moodle-php
    networks:
      - learning-hub-net

  # MySQL Database
  mysql:
    image: mysql:8.4
    container_name: learning-hub-mysql
    healthcheck:
      test: ["CMD", "mysqladmin", "ping", "-h", "localhost", "-u", "root", "-p$$MYSQL_ROOT_PASSWORD"]
      interval: 30s
      timeout: 10s
      retries: 5
      start_period: 60s
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: ${MYSQL_ROOT_PASSWORD}
      MYSQL_DATABASE: ${MYSQL_DATABASE}
      MYSQL_USER: ${MYSQL_USER}
      MYSQL_PASSWORD: ${MYSQL_PASSWORD}
    volumes:
      - ./moodle-docker/mysql/custom.cnf:/etc/mysql/conf.d/custom.cnf:ro
      - learning-hub-mysql-data:/var/lib/mysql
    networks:
      - learning-hub-net

  # Redis Cache
  redis:
    image: redis:7-alpine
    container_name: learning-hub-redis
    healthcheck:
      test: ["CMD", "redis-cli", "ping"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 30s
    restart: unless-stopped
    command: redis-server --appendonly yes --maxmemory 256mb --maxmemory-policy allkeys-lru
    volumes:
      - learning-hub-redis-data:/data
    networks:
      - learning-hub-net

volumes:
  learning-hub-mysql-data:
  learning-hub-redis-data:

networks:
  learning-hub-net:
    driver: bridge
