# Hybrid React + Moodle Learning Hub Configuration
# This configuration routes traffic between React frontend and Moodle backend

# Upstream definitions
upstream react_frontend {
    server react-frontend:3001;
}

upstream moodle_backend {
    server moodle-php:9000;
}

# Main server block
server {
    listen 80;
    server_name localhost;
    
    # Security headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Health check endpoint
    location /nginx-health {
        access_log off;
        return 200 "healthy\n";
        add_header Content-Type text/plain;
    }

    # React Frontend Routes
    # Main page, departments, library, classroom
    location ~ ^/(departments|library|classroom|dashboard|FinanceDept|ConstructionDept|TowerTechnicians|HumanRelationsDept|SalesDept|NetworkOperations|LeadershipTeam)(/.*)?$ {
        proxy_pass http://react_frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        proxy_read_timeout 86400;
    }

    # React static assets
    location ~ ^/(static|assets)/ {
        proxy_pass http://react_frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        
        # Cache static assets
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # React development hot reload
    location /sockjs-node {
        proxy_pass http://react_frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection "upgrade";
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Moodle Backend Routes
    # All /learning/* routes go to Moodle
    location /learning {
        alias /var/www/html;
        index index.php index.html index.htm;
        try_files $uri $uri/ @moodle;
    }

    # Moodle PHP handling
    location @moodle {
        rewrite ^/learning(.*)$ $1 break;
        fastcgi_pass moodle_backend;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME /var/www/html$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_param REQUEST_URI /learning$request_uri;
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 256 16k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_temp_file_write_size 256k;
    }

    # Moodle PHP files
    location ~ ^/learning/.*\.php$ {
        rewrite ^/learning(.*)$ $1 break;
        fastcgi_pass moodle_backend;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME /var/www/html$fastcgi_script_name;
        include fastcgi_params;
        fastcgi_param REQUEST_URI /learning$request_uri;
        fastcgi_read_timeout 300;
        fastcgi_buffer_size 128k;
        fastcgi_buffers 256 16k;
        fastcgi_busy_buffers_size 256k;
        fastcgi_temp_file_write_size 256k;
    }

    # Moodle static files
    location ~ ^/learning/.*\.(jpg|jpeg|gif|png|css|js|ico|xml|svg|woff|woff2|ttf|eot)$ {
        rewrite ^/learning(.*)$ $1 break;
        root /var/www/html;
        expires 1y;
        add_header Cache-Control "public, immutable";
        access_log off;
    }

    # Moodle dataroot protection
    location /learning/moodledata {
        internal;
        alias /var/moodledata;
    }

    # API endpoints for React-Moodle integration
    location /api/moodle {
        rewrite ^/api/moodle(.*)$ /learning/webservice/rest/server.php$1 break;
        fastcgi_pass moodle_backend;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME /var/www/html/webservice/rest/server.php;
        include fastcgi_params;
        fastcgi_read_timeout 300;
        
        # CORS headers for API access
        add_header Access-Control-Allow-Origin "*" always;
        add_header Access-Control-Allow-Methods "GET, POST, OPTIONS" always;
        add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization" always;
        
        if ($request_method = 'OPTIONS') {
            add_header Access-Control-Allow-Origin "*";
            add_header Access-Control-Allow-Methods "GET, POST, OPTIONS";
            add_header Access-Control-Allow-Headers "Origin, X-Requested-With, Content-Type, Accept, Authorization";
            add_header Content-Length 0;
            add_header Content-Type text/plain;
            return 200;
        }
    }

    # Root route - serve React app
    location / {
        proxy_pass http://react_frontend;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Fallback to React for client-side routing
        try_files $uri $uri/ @react;
    }

    location @react {
        proxy_pass http://react_frontend;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Deny access to sensitive files
    location ~ /\.ht {
        deny all;
    }

    location ~ /\.git {
        deny all;
    }

    location ~ /\.env {
        deny all;
    }

    # Rate limiting for sensitive endpoints
    location /learning/login {
        limit_req zone=login burst=5 nodelay;
        try_files $uri @moodle;
    }

    location /learning/webservice {
        limit_req zone=api burst=20 nodelay;
        try_files $uri @moodle;
    }

    error_log /var/log/nginx/learning_hub_error.log warn;
    access_log /var/log/nginx/learning_hub_access.log main;
}

# SSL/HTTPS server block (for production)
server {
    listen 443 ssl http2;
    server_name localhost;
    
    ssl_certificate /etc/nginx/certs/selfsigned.crt;
    ssl_certificate_key /etc/nginx/certs/selfsigned.key;
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security headers
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;

    # Include all the same location blocks as HTTP server
    # (This would be a copy of all the location blocks above)
    # For brevity, redirecting HTTP to HTTPS in production
    
    error_log /var/log/nginx/learning_hub_ssl_error.log warn;
    access_log /var/log/nginx/learning_hub_ssl_access.log main;
}
