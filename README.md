# FatbeamU - Hybrid React + Moodle Learning Hub

This repository contains the hybrid React + Moodle learning management system for FatbeamU, featuring a seamless integration between a React frontend and Moodle backend.

## 🏗️ Architecture Overview

The system combines the best of both worlds:
- **React Frontend**: Custom pages (departments, library, main page, classroom)
- **Moodle Backend**: Full-featured learning management system
- **Nginx Reverse Proxy**: Intelligent routing between systems
- **Unified User Experience**: Seamless navigation and shared authentication

## 🚀 Quick Start

### Prerequisites
- Docker and Docker Compose
- Git

### 1. Clone and Start Services
```bash
# Start all services
docker-compose up --build

# Or run in background
docker-compose up --build -d
```

### 2. Access the System
- **Main Hub**: http://localhost:3000 (React Frontend)
- **Learning System**: http://localhost:3000/learning (Moodle Backend)
- **Admin Panel**: http://localhost:3000/learning/admin

### 3. Default Credentials
- **Admin Username**: FatbeamU_admin
- **Admin Password**: !!Fatbeam25

## 📁 Project Structure

```
learning-hub/
├── docker-compose.yml          # Main orchestration
├── nginx/                      # Reverse proxy configuration
│   ├── nginx.conf
│   └── conf.d/default.conf
├── react-app/                  # React frontend application
│   ├── src/
│   │   ├── components/         # Reusable components
│   │   ├── pages/              # Page components
│   │   │   ├── MainPage.js     # Landing page
│   │   │   ├── LibraryPages.js # Library/resources
│   │   │   ├── ClassroomPage.js # Personal dashboard
│   │   │   └── departments/    # Department pages
│   │   └── services/           # API integration
│   └── public/
├── moodle-custom-theme/        # Custom Moodle theme
└── moodle-docker/              # Existing Moodle setup
```

## 🎯 Features

### React Frontend
- **Responsive Design**: Mobile-first approach with Tailwind CSS (properly installed)
- **Department Pages**: Specialized content for each department
- **Interactive Library**: Magical book browsing experience with Wiki integration
- **Personal Classroom**: Individualized learning dashboard
- **Unified Navigation**: Seamless routing between systems
- **Wiki Access**: Direct access to Moodle wiki from library interface

### Moodle Integration
- **Custom Theme**: Branded to match React frontend
- **API Integration**: Real-time data synchronization
- **Single Sign-On**: Shared authentication state
- **Course Management**: Full LMS capabilities
- **Wiki Module**: Collaborative documentation and knowledge base
- **Content Creation**: Create and edit wiki pages directly from React interface

### Nginx Routing
- **Intelligent Routing**: Automatic direction to appropriate system
- **Static Asset Optimization**: Efficient caching and delivery
- **CORS Configuration**: Secure cross-origin requests
- **SSL Ready**: HTTPS configuration for production

## 🔧 Development

### Dependencies & Requirements

**React Frontend:**
- Node.js 18+
- Tailwind CSS 3.4+ (properly configured)
- PostCSS & Autoprefixer
- React Router DOM 6.3+
- Axios for API calls

**Moodle Backend:**
- PHP 8.1+
- MySQL 8.0+
- Moodle 5.0 with Wiki module enabled
- Web services enabled for API access

### React Development
```bash
# React files auto-reload via Docker volumes
# Edit files in react-app/src/ and see changes immediately

# Install new dependencies (if needed)
cd react-app
npm install

# Build Tailwind CSS
npm run build
```

### Moodle Development
```bash
# Access Moodle container
docker exec -it learning-hub-moodle bash

# Edit theme files in moodle-custom-theme/
# Changes require cache clearing in Moodle admin
```

### API Integration
The React frontend communicates with Moodle via REST API:
- **Endpoint**: `/learning/webservice/rest/server.php`
- **Authentication**: Token-based
- **CORS**: Configured for cross-origin requests

## 🌐 Routing Strategy

| Route Pattern | Destination | Purpose |
|---------------|-------------|---------|
| `/` | React | Landing page |
| `/departments/*` | React | Department pages |
| `/library` | React | Resource library with wiki integration |
| `/classroom` | React | Personal dashboard |
| `/learning/*` | Moodle | LMS functionality |
| `/learning/mod/wiki/*` | Moodle Wiki | Wiki pages and documentation |
| `/api/moodle/*` | Moodle API | Data integration |

## 🎨 Department Pages

The system includes specialized pages for each department:

1. **Financial Department (Alchemists)** - ✅ Complete
2. **Construction Department (Earth Mages)** - 🚧 Placeholder
3. **Tower Technicians (Sky Weavers)** - 🚧 Placeholder
4. **Human Relations (Mind Readers)** - 🚧 Placeholder
5. **Sales Department (Persuasion Sorcerers)** - 🚧 Placeholder
6. **Network Operations (Data Weavers)** - 🚧 Placeholder
7. **Leadership Team (Council of Archmages)** - 🚧 Placeholder

## 🔐 Security Features

- **CORS Protection**: Configured for secure API access
- **Rate Limiting**: Protection against abuse
- **Security Headers**: XSS, CSRF, and clickjacking protection
- **File Access Control**: Sensitive files protected
- **SSL/TLS Ready**: Production-ready HTTPS configuration

## 📊 Monitoring & Logs

```bash
# View all logs
docker-compose logs -f

# Specific service logs
docker-compose logs -f react-frontend
docker-compose logs -f learning-hub-moodle
docker-compose logs -f learning-hub-nginx

# Health checks
curl http://localhost:3000/nginx-health
```

## 🚀 Production Deployment

### Environment Variables
Create a `.env` file with production values:
```bash
# Moodle Configuration
MOODLE_WWWROOT=https://your-domain.com/learning
MOODLE_DB_HOST=your-db-host
MOODLE_DB_NAME=your-db-name
# ... other variables

# React Configuration
REACT_APP_MOODLE_URL=https://your-domain.com/learning
REACT_APP_MOODLE_TOKEN=your-api-token
```

### SSL Configuration
1. Place SSL certificates in `nginx/certs/`
2. Update nginx configuration for your domain
3. Configure DNS to point to your server

## 🔄 Migration from Previous Setup

This branch represents a complete transformation from the previous Moodle-only setup to a hybrid architecture. The migration preserves:
- ✅ All Moodle data and configurations
- ✅ User accounts and course content
- ✅ Existing admin credentials
- ✅ Database and file storage

## 🛠️ Troubleshooting

### Common Issues

**CORS Errors**
- Check nginx configuration in `nginx/conf.d/default.conf`
- Verify API endpoints are properly proxied

**React Hot Reload Not Working**
- Ensure `CHOKIDAR_USEPOLLING=true` in React environment
- Check Docker volume mounts

**Moodle API Calls Failing**
- Verify web services are enabled in Moodle admin
- Check API token configuration
- Review CORS headers

**Database Connection Issues**
- Check MySQL container health
- Verify environment variables
- Review database credentials

### Health Checks
```bash
# Check service status
docker-compose ps

# Test nginx health
curl http://localhost:3000/nginx-health

# Test React frontend
curl http://localhost:3000

# Test Moodle backend
curl http://localhost:3000/learning
```

## 📚 Documentation

- [Moodle Web Services API](https://docs.moodle.org/dev/Web_service_API_functions)
- [React Router Documentation](https://reactrouter.com/)
- [Nginx Configuration Guide](https://nginx.org/en/docs/)
- [Docker Compose Reference](https://docs.docker.com/compose/)

## 🤝 Contributing

1. Create a feature branch from `hybrid-react-moodle-learning-hub`
2. Make your changes
3. Test thoroughly in development environment
4. Submit a pull request with detailed description

## 📝 License

This project is licensed under the same terms as the original FatbeamU project.