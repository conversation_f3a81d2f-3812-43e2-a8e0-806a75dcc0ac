<?php
// This file is part of Moodle - http://moodle.org/
//
// Moodle is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Learning Hub Custom Theme Configuration
 *
 * @package   theme_learning_hub
 * @copyright 2024 Fatbeam University
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

$THEME->name = 'learning_hub';
$THEME->sheets = ['custom'];
$THEME->editor_sheets = [];
$THEME->parents = ['boost'];
$THEME->enable_dock = false;

$THEME->rendererfactory = 'theme_overridden_renderer_factory';
$THEME->csspostprocess = 'theme_learning_hub_process_css';

// Layout files
$THEME->layouts = [
    'base' => [
        'file' => 'columns2.php',
        'regions' => ['side-pre'],
        'defaultregion' => 'side-pre',
    ],
    'standard' => [
        'file' => 'columns2.php',
        'regions' => ['side-pre'],
        'defaultregion' => 'side-pre',
    ],
    'course' => [
        'file' => 'course.php',
        'regions' => ['side-pre'],
        'defaultregion' => 'side-pre',
        'options' => ['langmenu' => true],
    ],
    'coursecategory' => [
        'file' => 'columns2.php',
        'regions' => ['side-pre'],
        'defaultregion' => 'side-pre',
    ],
    'incourse' => [
        'file' => 'columns2.php',
        'regions' => ['side-pre'],
        'defaultregion' => 'side-pre',
    ],
    'frontpage' => [
        'file' => 'frontpage.php',
        'regions' => ['side-pre'],
        'defaultregion' => 'side-pre',
        'options' => ['nonavbar' => true],
    ],
    'admin' => [
        'file' => 'columns2.php',
        'regions' => ['side-pre'],
        'defaultregion' => 'side-pre',
    ],
    'mydashboard' => [
        'file' => 'columns2.php',
        'regions' => ['side-pre'],
        'defaultregion' => 'side-pre',
        'options' => ['nonavbar' => true, 'langmenu' => true],
    ],
    'mypublic' => [
        'file' => 'columns2.php',
        'regions' => ['side-pre'],
        'defaultregion' => 'side-pre',
    ],
    'login' => [
        'file' => 'login.php',
        'regions' => [],
        'options' => ['langmenu' => true, 'nonavbar' => true],
    ],
    'popup' => [
        'file' => 'popup.php',
        'regions' => [],
        'options' => ['nofooter' => true, 'nonavbar' => true],
    ],
    'frametop' => [
        'file' => 'columns1.php',
        'regions' => [],
        'options' => ['nofooter' => true, 'nocoursefooter' => true],
    ],
    'embedded' => [
        'file' => 'embedded.php',
        'regions' => []
    ],
    'maintenance' => [
        'file' => 'maintenance.php',
        'regions' => [],
    ],
    'print' => [
        'file' => 'columns1.php',
        'regions' => [],
        'options' => ['nofooter' => true, 'nonavbar' => false],
    ],
    'redirect' => [
        'file' => 'embedded.php',
        'regions' => [],
    ],
    'report' => [
        'file' => 'columns2.php',
        'regions' => ['side-pre'],
        'defaultregion' => 'side-pre',
    ],
    'secure' => [
        'file' => 'secure.php',
        'regions' => ['side-pre'],
        'defaultregion' => 'side-pre'
    ]
];

// Custom settings
$THEME->settings = [
    'react_frontend_url' => [
        'type' => 'text',
        'default' => 'http://localhost:3000',
        'title' => 'React Frontend URL',
        'description' => 'URL of the React frontend application'
    ],
    'show_react_navigation' => [
        'type' => 'checkbox',
        'default' => true,
        'title' => 'Show React Navigation',
        'description' => 'Display navigation links back to React frontend'
    ],
    'custom_logo' => [
        'type' => 'file',
        'title' => 'Custom Logo',
        'description' => 'Upload a custom logo for the learning hub'
    ]
];

// JavaScript files to include
$THEME->javascripts = [];

// Additional CSS processing
$THEME->scss = function($theme) {
    return theme_learning_hub_get_main_scss_content($theme);
};

$THEME->extrascsscallback = 'theme_learning_hub_get_extra_scss';
$THEME->prescsscallback = 'theme_learning_hub_get_pre_scss';
$THEME->precompiledcsscallback = 'theme_learning_hub_get_precompiled_css';

// Icon system
$THEME->iconsystem = \core\output\icon_system::FONTAWESOME;

// Course index usage
$THEME->usescourseindex = true;

// Activity header configuration
$THEME->activityheaderconfig = [
    'notitle' => true
];

// Block configuration
$THEME->addblockposition = BLOCK_ADDBLOCK_POSITION_FLATNAV;
