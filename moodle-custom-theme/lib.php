<?php
// This file is part of Moodle - http://moodle.org/
//
// <PERSON><PERSON><PERSON> is free software: you can redistribute it and/or modify
// it under the terms of the GNU General Public License as published by
// the Free Software Foundation, either version 3 of the License, or
// (at your option) any later version.
//
// Moodle is distributed in the hope that it will be useful,
// but WITHOUT ANY WARRANTY; without even the implied warranty of
// MERCHANTABILITY or FITNESS FOR A PARTICULAR PURPOSE.  See the
// GNU General Public License for more details.
//
// You should have received a copy of the GNU General Public License
// along with Moodle.  If not, see <http://www.gnu.org/licenses/>.

/**
 * Learning Hub Custom Theme Library Functions
 *
 * @package   theme_learning_hub
 * @copyright 2024 Fatbeam University
 * @license   http://www.gnu.org/copyleft/gpl.html GNU GPL v3 or later
 */

defined('MOODLE_INTERNAL') || die();

/**
 * Get the main SCSS content for the theme
 *
 * @param theme_config $theme The theme config object.
 * @return string
 */
function theme_learning_hub_get_main_scss_content($theme) {
    global $CFG;

    $scss = '';
    $filename = !empty($theme->settings->preset) ? $theme->settings->preset : null;
    $fs = get_file_storage();

    $context = context_system::instance();
    if ($filename == 'default.scss') {
        $scss .= file_get_contents($CFG->dirroot . '/theme/boost/scss/preset/default.scss');
    } else if ($filename == 'plain.scss') {
        $scss .= file_get_contents($CFG->dirroot . '/theme/boost/scss/preset/plain.scss');
    } else if ($filename && ($presetfile = $fs->get_file($context->id, 'theme_learning_hub', 'preset', 0, '/', $filename))) {
        $scss .= $presetfile->get_content();
    } else {
        // Safety fallback - should never happen.
        $scss .= file_get_contents($CFG->dirroot . '/theme/boost/scss/preset/default.scss');
    }

    // Add custom SCSS for Learning Hub integration
    $scss .= '
    // Learning Hub Custom Styles
    .learning-hub-nav {
        background: linear-gradient(135deg, #15a7dd 0%, #1397c7 100%);
        padding: 0.5rem 1rem;
        margin-bottom: 1rem;
        border-radius: 0.5rem;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.1);
    }

    .learning-hub-nav a {
        color: white;
        text-decoration: none;
        font-weight: 500;
        display: inline-flex;
        align-items: center;
        transition: opacity 0.3s ease;
    }

    .learning-hub-nav a:hover {
        opacity: 0.8;
        text-decoration: none;
        color: white;
    }

    .learning-hub-nav i {
        margin-right: 0.5rem;
    }

    // Custom branding
    .navbar-brand {
        font-family: serif;
        font-weight: bold;
    }

    // Fatbeam colors
    :root {
        --fatbeam-blue: #15a7dd;
        --fatbeam-dark: #1397c7;
        --fatbeam-purple: #6a3293;
    }

    // Primary color overrides
    .btn-primary {
        background-color: var(--fatbeam-blue);
        border-color: var(--fatbeam-blue);
    }

    .btn-primary:hover {
        background-color: var(--fatbeam-dark);
        border-color: var(--fatbeam-dark);
    }

    // Navigation styling
    .navbar-light .navbar-nav .nav-link {
        color: rgba(0, 0, 0, 0.7);
    }

    .navbar-light .navbar-nav .nav-link:hover {
        color: var(--fatbeam-blue);
    }

    // Course styling
    .course-content .section .content {
        margin-left: 0;
    }

    // Activity styling
    .activity .activityinstance {
        padding: 1rem;
        border-radius: 0.5rem;
        border: 1px solid #e9ecef;
        margin-bottom: 1rem;
        transition: box-shadow 0.3s ease;
    }

    .activity .activityinstance:hover {
        box-shadow: 0 2px 8px rgba(21, 167, 221, 0.15);
    }

    // Footer styling
    #page-footer {
        background: linear-gradient(135deg, #f8f9fa 0%, #e9ecef 100%);
        border-top: 3px solid var(--fatbeam-blue);
    }
    ';

    return $scss;
}

/**
 * Get extra SCSS content
 *
 * @param theme_config $theme The theme config object.
 * @return string
 */
function theme_learning_hub_get_extra_scss($theme) {
    $content = '';
    
    // Add any extra SCSS from theme settings
    if (!empty($theme->settings->scss)) {
        $content .= $theme->settings->scss;
    }

    return $content;
}

/**
 * Get pre SCSS content
 *
 * @param theme_config $theme The theme config object.
 * @return string
 */
function theme_learning_hub_get_pre_scss($theme) {
    $scss = '';
    
    // Add any pre-SCSS variables
    $scss .= '$primary: #15a7dd !default;';
    $scss .= '$secondary: #6a3293 !default;';
    $scss .= '$success: #28a745 !default;';
    $scss .= '$info: #17a2b8 !default;';
    $scss .= '$warning: #ffc107 !default;';
    $scss .= '$danger: #dc3545 !default;';
    
    return $scss;
}

/**
 * Get precompiled CSS
 *
 * @param theme_config $theme The theme config object.
 * @return string
 */
function theme_learning_hub_get_precompiled_css($theme) {
    global $CFG;
    
    $css = '';
    
    // Include any precompiled CSS files
    if (file_exists($CFG->dirroot . '/theme/learning_hub/style/custom.css')) {
        $css .= file_get_contents($CFG->dirroot . '/theme/learning_hub/style/custom.css');
    }
    
    return $css;
}

/**
 * Process CSS content
 *
 * @param string $css The CSS content
 * @param theme_config $theme The theme config object
 * @return string
 */
function theme_learning_hub_process_css($css, $theme) {
    // Add any CSS processing here
    return $css;
}

/**
 * Add React navigation to page
 *
 * @return string HTML for React navigation
 */
function theme_learning_hub_get_react_navigation() {
    global $CFG;
    
    $reacturl = get_config('theme_learning_hub', 'react_frontend_url') ?: 'http://localhost:3000';
    $shownavigation = get_config('theme_learning_hub', 'show_react_navigation');
    
    if (!$shownavigation) {
        return '';
    }
    
    $html = '
    <div class="learning-hub-nav">
        <a href="' . $reacturl . '" title="Return to Learning Hub">
            <i class="fa fa-arrow-left"></i>
            Back to Learning Hub
        </a>
    </div>';
    
    return $html;
}

/**
 * Get custom logo URL
 *
 * @return string|null
 */
function theme_learning_hub_get_logo_url() {
    global $OUTPUT;
    
    $logo = get_config('theme_learning_hub', 'custom_logo');
    if ($logo) {
        return $OUTPUT->image_url('custom_logo', 'theme_learning_hub');
    }
    
    return null;
}
